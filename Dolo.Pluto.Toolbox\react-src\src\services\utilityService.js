import neuService from './neuService';
class UtilityService {
  /**
   * Clean up a directory by removing all files and subdirectories except .lic files
   * @param {string} directoryPath - Path to the directory to clean up
   * @param {Function} onProgress - Optional callback function for progress updates
   * @returns {Promise<void>}
   */
  async cleanupDirectory(directoryPath, onProgress = null) {
    try {
      console.info(`[UtilityService] Cleaning up directory: ${directoryPath}`);

      if (onProgress) {
        onProgress({
          progress: 0,
          phase: 'Cleaning up directory...'
        });
      }

      const exists = await neuService.exists(directoryPath);

      if (!exists) {
        console.debug(`[UtilityService] Directory does not exist, creating: ${directoryPath}`);
        await neuService.createDirectory(directoryPath);
        return;
      }

      try {
        // Get all files and directories in the target directory
        const items = await neuService.readDirectory(directoryPath);
        console.debug(`[UtilityService] Found ${items.length} items to process in directory`);

        // Separate .lic files from other items
        const licenseFiles = [];
        const itemsToDelete = [];

        for (const item of items) {
          const itemPath = `${directoryPath}/${item}`;
          const isFile = await this.isFile(itemPath);

          if (isFile && item.toLowerCase().endsWith('.lic')) {
            licenseFiles.push(item);
            console.info(`[UtilityService] Preserving license file: ${item}`);
          } else {
            itemsToDelete.push(item);
          }
        }

        if (onProgress) {
          onProgress({
            progress: 25,
            phase: `Preserving ${licenseFiles.length} license files...`
          });
        }

        // Delete all items except .lic files
        for (let i = 0; i < itemsToDelete.length; i++) {
          const item = itemsToDelete[i];
          const itemPath = `${directoryPath}/${item}`;

          try {
            await neuService.remove(itemPath);
            console.debug(`[UtilityService] Deleted: ${item}`);
          } catch (deleteError) {
            console.warn(`[UtilityService] Failed to delete ${item}: ${deleteError.message}`);
          }

          if (onProgress) {
            const progress = 25 + Math.floor((i + 1) / itemsToDelete.length * 75);
            onProgress({
              progress,
              phase: `Cleaning files... (${i + 1}/${itemsToDelete.length})`
            });
          }
        }

        console.info(`[UtilityService] Directory cleaned selectively: ${directoryPath} (preserved ${licenseFiles.length} license files)`);
      } catch (error) {
        console.error(`[UtilityService] Error during selective cleanup, falling back to recreation: ${error.message}`);

        // Fallback to old behavior if selective cleanup fails
        try {
          await neuService.remove(directoryPath);
          await neuService.createDirectory(directoryPath);
          console.info(`[UtilityService] Directory cleaned by recreation (fallback): ${directoryPath}`);
        } catch (fallbackError) {
          console.error(`[UtilityService] Fallback cleanup also failed: ${fallbackError.message}`);
          throw fallbackError;
        }
      }

      if (onProgress) {
        onProgress({
          progress: 100,
          phase: 'Directory cleanup completed'
        });
      }
    } catch (error) {
      console.error(`[UtilityService] Error during directory cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extract a ZIP file to a target directory using tar
   * @param {string} zipFilePath - Path to the ZIP file
   * @param {string} targetDirectory - Directory to extract to
   * @param {Function} onProgress - Optional callback function for progress updates
   * @returns {Promise<void>}
   */
  async extractZipFile(zipFilePath, targetDirectory, onProgress = null) {
    try {
      console.info(`[UtilityService] Extracting zip file: ${zipFilePath} to ${targetDirectory}`);

      if (onProgress) {
        onProgress({
          progress: 0,
          phase: 'Starting extraction...'
        });
      }

      const formattedZipPath = zipFilePath.replace(/\\/g, '/');
      const formattedTargetDir = targetDirectory.replace(/\\/g, '/');

      const extractCommand = `tar -xf "${formattedZipPath}" -C "${formattedTargetDir}"`;

      console.debug(`[UtilityService] Running extraction command: ${extractCommand}`);

      const result = await neuService.execCommand(extractCommand);

      if (result.exitCode !== 0) {
        console.error(`[UtilityService] Tar extraction failed with exit code ${result.exitCode}`);
        console.error(`[UtilityService] Stderr: ${result.stdErr}`);

        console.info('[UtilityService] Falling back to platform-specific extraction');

        const osInfo = await neuService.getOSInfo();
        let fallbackCommand;

        if (osInfo.name === 'windows') {
          fallbackCommand = `powershell -command "Expand-Archive -Path '${formattedZipPath}' -DestinationPath '${formattedTargetDir}' -Force"`;
        } else {
          fallbackCommand = `unzip -o '${formattedZipPath}' -d '${formattedTargetDir}'`;
        }

        console.debug(`[UtilityService] Running fallback extraction command: ${fallbackCommand}`);

        const fallbackResult = await neuService.execCommand(fallbackCommand);

        if (fallbackResult.exitCode !== 0) {
          throw new Error(`Extraction failed with code ${fallbackResult.exitCode}: ${fallbackResult.stdErr}`);
        }
      }

      console.info(`[UtilityService] Extraction complete: ${zipFilePath}`);

      await neuService.remove(zipFilePath);

      if (onProgress) {
        onProgress({
          progress: 100,
          phase: 'Extraction complete'
        });
      }    } catch (error) {
      console.error(`[UtilityService] Extraction failed: ${error.message}`);
      
      // Provide user-friendly error message for extraction failures
      let userFriendlyMessage = 'Failed to extract files';
      
      if (error.message?.includes('Extraction failed with code')) {
        userFriendlyMessage = 'File extraction failed. The download may be corrupted.';
      } else if (error.message?.includes('permission') || error.message?.includes('access')) {
        userFriendlyMessage = 'Permission denied during extraction. Try running as administrator.';
      } else if (error.message?.includes('space') || error.message?.includes('ENOSPC')) {
        userFriendlyMessage = 'Not enough disk space for extraction.';
      } else if (error.message?.includes('format') || error.message?.includes('corrupt')) {
        userFriendlyMessage = 'Invalid or corrupted archive file.';
      }
      
      if (onProgress) {
        onProgress({
          progress: 0,
          phase: userFriendlyMessage,
          error: userFriendlyMessage,
          resetState: true
        });
      }
      
      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * Check if a path is a file (not a directory)
   * @param {string} filePath - Path to check
   * @returns {Promise<boolean>} - True if it's a file, false if it's a directory
   */
  async isFile(filePath) {
    try {
      const stats = await neuService.getStats(filePath);
      return stats.isFile;
    } catch (error) {
      console.warn(`[UtilityService] Error checking if ${filePath} is a file: ${error.message}`);
      return false;
    }
  }

  /**
   * List contents of a directory
   * @param {string} directoryPath - Path to the directory
   * @returns {Promise<string[]>} - Array of file and directory names
   */
  async listDirectoryContents(directoryPath) {
    try {
      return await neuService.readDirectory(directoryPath);
    } catch (error) {
      console.error(`[UtilityService] Error listing directory ${directoryPath}:`, error);
      return [];
    }
  }

  /**
   * Convert ArrayBuffer to Base64 string
   * @param {Uint8Array} buffer - The buffer to convert
   * @returns {string} - Base64 string
   */
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;

    const chunkSize = 10000;

    for (let i = 0; i < len; i += chunkSize) {
      const chunk = bytes.slice(i, Math.min(i + chunkSize, len));
      binary += String.fromCharCode.apply(null, chunk);
    }

    return btoa(binary);
  }

     /**
     * Delay function to pause execution for a specified time
     * @param {number} ms - Delay in milliseconds
     * @returns {Promise<void>} - Resolves after the delay
     */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
   

  /**
   * Compare semantic version strings
   * @param {string} v1 - First version string (e.g. "1.2.3")
   * @param {string} v2 - Second version string (e.g. "1.3.0")
   * @returns {number} - Positive if v1 > v2, negative if v1 < v2, 0 if equal
   */
  compareVersions(v1, v2) {
    if (!v1) return v2 ? -1 : 0;
    if (!v2) return 1;
    
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const p1 = parts1[i] || 0;
      const p2 = parts2[i] || 0;
      
      if (p1 !== p2) return p1 - p2;
    }
    
    return 0;
  }
}

const utilityService = new UtilityService();
export default utilityService;