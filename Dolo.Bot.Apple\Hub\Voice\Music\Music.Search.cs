using Dolo.Bot.Apple.Hub.Voice.Music.Services;
using Dolo.Database;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("search")]
    [Description("search for music and select from results")]
    public async Task SearchAsync(SlashCommandContext ctx, 
        [Description("search query")] string query)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        try
        {
            var tracks = await SearchService.SearchYouTubeAsync(query, ctx.User, 5);
            if (tracks.Count == 0)
            {
                await ctx.TryEditResponseAsync(MusicEmbeds.Error("No results found for your search query."));
                return;
            }

            // Create selection buttons
            var buttons = new List<DiscordComponent>();
            for (int i = 0; i < Math.Min(tracks.Count, 5); i++)
            {
                buttons.Add(new DiscordButtonComponent(
                    DiscordButtonStyle.Primary, 
                    $"music_select_{i}_{ctx.User.Id}", 
                    $"{i + 1}", 
                    false, 
                    new DiscordComponentEmoji("🎵")));
            }

            buttons.Add(new DiscordButtonComponent(
                DiscordButtonStyle.Danger, 
                $"music_cancel_{ctx.User.Id}", 
                "Cancel", 
                false, 
                new DiscordComponentEmoji("❌")));

            var actionRow = new DiscordActionRowComponent(buttons);
            var webhookBuilder = new DiscordWebhookBuilder()
                .AddEmbed(MusicEmbeds.SearchResults(tracks, query))
                .AddActionRowComponent(actionRow);

            await ctx.TryEditResponseAsync(webhookBuilder);

            // Store search results temporarily (in a real implementation, you'd use a proper cache)
            // For now, this is a placeholder for the interaction handling
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error in search command: {ex.Message}");
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("An error occurred while searching."));
        }
    }

    [Command("playlist")]
    [Description("add a playlist to the queue")]
    public async Task PlaylistAsync(SlashCommandContext ctx, 
        [Description("playlist URL")] string playlistUrl)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        // Get voice channel info
        var voiceUser = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceUser == null)
        {
            await ctx.TryEditResponseAsync("This text channel is not linked to a voice channel.");
            return;
        }

        // Check if user is in the voice channel
        var member = ctx.Member;
        var memberVoiceChannel = member?.VoiceState != null ? await member.VoiceState.GetChannelAsync() : null;
        if (memberVoiceChannel?.Id != voiceUser.Channel)
        {
            await ctx.TryEditResponseAsync("You must be in the voice channel to use music commands.");
            return;
        }

        try
        {
            // Get or create music player
            var player = await MusicService.GetOrCreatePlayerAsync(ctx.Guild.Id, voiceUser.Channel, ctx.Channel.Id);
            if (player == null)
            {
                await ctx.TryEditResponseAsync("Failed to connect to voice channel.");
                return;
            }

            // Get playlist tracks
            var tracks = await SearchService.GetPlaylistAsync(playlistUrl, ctx.User);
            if (tracks.Count == 0)
            {
                await ctx.TryEditResponseAsync(MusicEmbeds.Error("No tracks found in the playlist or invalid URL."));
                return;
            }

            // Add all tracks to queue
            foreach (var track in tracks)
            {
                player.Queue.Enqueue(track);
            }

            // Start playing if not already playing
            if (!player.IsPlaying && !player.IsPaused)
            {
                var firstTrack = player.Queue.Dequeue();
                if (firstTrack != null)
                {
                    _ = Task.Run(async () => await MusicService.PlayAsync(player, firstTrack));
                }
            }

            await ctx.TryEditResponseAsync(MusicEmbeds.PlaylistAdded(tracks, ctx.User));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error in playlist command: {ex.Message}");
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("An error occurred while adding the playlist."));
        }
    }

    [Command("playnext")]
    [Description("add a song to play next in the queue")]
    public async Task PlayNextAsync(SlashCommandContext ctx, 
        [Description("song name, URL, or search query")] string query)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        // Get voice channel info
        var voiceUser = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceUser == null)
        {
            await ctx.TryEditResponseAsync("This text channel is not linked to a voice channel.");
            return;
        }

        // Check if user is in the voice channel
        var member = ctx.Member;
        var memberVoiceChannel2 = member?.VoiceState != null ? await member.VoiceState.GetChannelAsync() : null;
        if (memberVoiceChannel2?.Id != voiceUser.Channel)
        {
            await ctx.TryEditResponseAsync("You must be in the voice channel to use music commands.");
            return;
        }

        try
        {
            // Get or create music player
            var player = await MusicService.GetOrCreatePlayerAsync(ctx.Guild.Id, voiceUser.Channel, ctx.Channel.Id);
            if (player == null)
            {
                await ctx.TryEditResponseAsync("Failed to connect to voice channel.");
                return;
            }

            // Search for tracks
            var tracks = await SearchService.SearchYouTubeAsync(query, ctx.User);
            if (tracks.Count == 0)
            {
                await ctx.TryEditResponseAsync(MusicEmbeds.Error("No results found for your search query."));
                return;
            }

            var track = tracks.First();
            
            // Add to front of queue
            player.Queue.EnqueueNext(track);
            
            await ctx.TryEditResponseAsync(MusicEmbeds.Success($"⏭️ **{track.FormattedTitle}** will play next!"));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error in playnext command: {ex.Message}");
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("An error occurred while adding the track."));
        }
    }
}
