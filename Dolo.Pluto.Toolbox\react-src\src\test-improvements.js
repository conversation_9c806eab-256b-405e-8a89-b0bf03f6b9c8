/**
 * Test script to verify the improved tool installation process
 * This script tests:
 * 1. License file preservation during cleanup
 * 2. Executable validation before tool registration
 */

import utilityService from './services/utilityService.js';
import toolService from './services/toolService.js';
import storageService from './services/storageService.js';
import neuService from './services/neuService.js';

class InstallationImprovementTests {
  constructor() {
    this.testResults = [];
    this.testDirectory = null;
  }

  /**
   * Log test results
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    this.testResults.push({ timestamp, type, message });
  }

  /**
   * Setup test environment
   */
  async setupTestEnvironment() {
    try {
      this.log('Setting up test environment...');
      
      // Create a temporary test directory
      this.testDirectory = `${await neuService.getPath('temp')}/toolbox-test-${Date.now()}`;
      await neuService.createDirectory(this.testDirectory);
      
      this.log(`Test directory created: ${this.testDirectory}`);
      return true;
    } catch (error) {
      this.log(`Failed to setup test environment: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Test license file preservation during cleanup
   */
  async testLicenseFilePreservation() {
    try {
      this.log('Testing license file preservation...');
      
      const testToolDir = `${this.testDirectory}/TestTool`;
      await neuService.createDirectory(testToolDir);
      
      // Create test files including a license file
      const testFiles = [
        'TestTool.exe',
        'config.json',
        'readme.txt',
        'TestTool.lic',  // License file that should be preserved
        'another.lic'    // Another license file
      ];
      
      for (const file of testFiles) {
        const filePath = `${testToolDir}/${file}`;
        await neuService.writeFile(filePath, `Test content for ${file}`);
      }
      
      this.log(`Created ${testFiles.length} test files`);
      
      // Verify all files exist before cleanup
      const filesBefore = await neuService.readDirectory(testToolDir);
      this.log(`Files before cleanup: ${filesBefore.join(', ')}`);
      
      if (filesBefore.length !== testFiles.length) {
        throw new Error(`Expected ${testFiles.length} files, found ${filesBefore.length}`);
      }
      
      // Perform cleanup
      await utilityService.cleanupDirectory(testToolDir);
      
      // Check which files remain
      const filesAfter = await neuService.readDirectory(testToolDir);
      this.log(`Files after cleanup: ${filesAfter.join(', ')}`);
      
      // Verify only .lic files remain
      const expectedRemainingFiles = testFiles.filter(f => f.endsWith('.lic'));
      
      if (filesAfter.length !== expectedRemainingFiles.length) {
        throw new Error(`Expected ${expectedRemainingFiles.length} files to remain, found ${filesAfter.length}`);
      }
      
      for (const expectedFile of expectedRemainingFiles) {
        if (!filesAfter.includes(expectedFile)) {
          throw new Error(`License file ${expectedFile} was not preserved`);
        }
      }
      
      this.log('✅ License file preservation test PASSED', 'success');
      return true;
    } catch (error) {
      this.log(`❌ License file preservation test FAILED: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Test executable validation before tool registration
   */
  async testExecutableValidation() {
    try {
      this.log('Testing executable validation...');
      
      const testToolId = 'TestValidationTool';
      const testToolDir = `${this.testDirectory}/${testToolId}`;
      await neuService.createDirectory(testToolDir);
      
      // Test 1: Try to add tool without executable (should fail)
      try {
        await toolService.addTool(testToolId, 'Test Validation Tool', '1.0.0');
        this.log('❌ Expected validation to fail when executable is missing', 'error');
        return false;
      } catch (error) {
        if (error.message.includes('Tool executable not found')) {
          this.log('✅ Correctly rejected tool without executable', 'success');
        } else {
          throw error;
        }
      }
      
      // Test 2: Create executable and try again (should succeed)
      const executablePath = `${testToolDir}/${testToolId}.exe`;
      await neuService.writeFile(executablePath, 'Mock executable content');
      
      try {
        await toolService.addTool(testToolId, 'Test Validation Tool', '1.0.0');
        this.log('✅ Successfully added tool with valid executable', 'success');
        
        // Verify tool was added to extensions
        const extensions = await storageService.readExtensionsFile();
        const addedTool = extensions.extensions.find(t => t.id === testToolId);
        
        if (!addedTool) {
          throw new Error('Tool was not added to extensions.json');
        }
        
        this.log('✅ Tool correctly registered in extensions.json', 'success');
      } catch (error) {
        throw new Error(`Failed to add tool with valid executable: ${error.message}`);
      }
      
      this.log('✅ Executable validation test PASSED', 'success');
      return true;
    } catch (error) {
      this.log(`❌ Executable validation test FAILED: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Cleanup test environment
   */
  async cleanupTestEnvironment() {
    try {
      if (this.testDirectory) {
        this.log('Cleaning up test environment...');
        await neuService.remove(this.testDirectory);
        this.log('Test environment cleaned up');
      }
    } catch (error) {
      this.log(`Warning: Failed to cleanup test environment: ${error.message}`, 'warn');
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    this.log('🧪 Starting installation improvement tests...');
    
    const setupSuccess = await this.setupTestEnvironment();
    if (!setupSuccess) {
      this.log('❌ Test setup failed, aborting tests', 'error');
      return false;
    }
    
    const tests = [
      { name: 'License File Preservation', fn: () => this.testLicenseFilePreservation() },
      { name: 'Executable Validation', fn: () => this.testExecutableValidation() }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
      this.log(`\n--- Running ${test.name} Test ---`);
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
    }
    
    await this.cleanupTestEnvironment();
    
    this.log(`\n🏁 Test Results: ${passedTests}/${tests.length} tests passed`);
    
    if (passedTests === tests.length) {
      this.log('🎉 All tests PASSED! Installation improvements are working correctly.', 'success');
      return true;
    } else {
      this.log('❌ Some tests FAILED. Please review the implementation.', 'error');
      return false;
    }
  }
}

// Export for use in other modules or manual testing
export default InstallationImprovementTests;

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Running in browser environment
  console.log('Installation improvement tests are available. Call new InstallationImprovementTests().runAllTests() to run them.');
}
