using DSharpPlus.VoiceNext;

namespace Dolo.Bot.Apple.Hub.Voice.Music.Entities;

public class MusicPlayer
{
    public ulong GuildId { get; }
    public ulong ChannelId { get; set; }
    public ulong TextChannelId { get; set; }
    public VoiceNextConnection? Connection { get; set; }
    public MusicQueue Queue { get; } = new();
    public PlayerState State { get; set; } = PlayerState.Stopped;
    public float Volume { get; set; } = 0.5f;
    public TimeSpan Position { get; set; }
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    public CancellationTokenSource? PlaybackCancellation { get; set; }
    
    private readonly Timer _inactivityTimer;
    private const int InactivityTimeoutMinutes = 5;

    public MusicPlayer(ulong guildId, ulong channelId, ulong textChannelId)
    {
        GuildId = guildId;
        ChannelId = channelId;
        TextChannelId = textChannelId;
        
        _inactivityTimer = new Timer(CheckInactivity, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public bool IsConnected => Connection != null;
    public bool IsPlaying => State == PlayerState.Playing;
    public bool IsPaused => State == PlayerState.Paused;
    public bool IsStopped => State == PlayerState.Stopped;
    
    public Track? CurrentTrack => Queue.CurrentTrack;
    public Track? NextTrack => Queue.GetNext();

    public void UpdateActivity()
    {
        LastActivity = DateTime.UtcNow;
    }

    public Task DisconnectAsync()
    {
        try
        {
            PlaybackCancellation?.Cancel();

            if (Connection != null)
            {
                Connection.Disconnect();
                Connection.Dispose();
                Connection = null;
            }

            State = PlayerState.Stopped;
            Queue.Clear();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error disconnecting player: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    private void CheckInactivity(object? state)
    {
        if (DateTime.UtcNow - LastActivity > TimeSpan.FromMinutes(InactivityTimeoutMinutes))
        {
            if (IsStopped || Queue.IsEmpty)
            {
                Console.WriteLine($"[Music] Player inactive for {InactivityTimeoutMinutes} minutes, disconnecting...");
                _ = Task.Run(async () => await DisconnectAsync());
            }
        }
    }

    public void Dispose()
    {
        _inactivityTimer?.Dispose();
        PlaybackCancellation?.Cancel();
        PlaybackCancellation?.Dispose();
        Connection?.Dispose();
    }
}

public enum PlayerState
{
    Stopped,
    Playing,
    Paused,
    Buffering
}
