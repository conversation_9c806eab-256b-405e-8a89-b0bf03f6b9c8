/*! For license information please see main.4af17a56.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),o=n(43),a=n(950);function l(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}var s=Symbol.for("react.element"),c=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),m=Symbol.for("react.provider"),h=Symbol.for("react.consumer"),g=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),w=Symbol.for("react.memo"),x=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var k=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var S=Symbol.for("react.memo_cache_sentinel"),N=Symbol.iterator;function E(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=N&&e[N]||e["@@iterator"])?e:null}var j=Symbol.for("react.client.reference");function C(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===j?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case d:return"Fragment";case u:return"Portal";case p:return"Profiler";case f:return"StrictMode";case b:return"Suspense";case y:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case g:return(e.displayName||"Context")+".Provider";case h:return(e._context.displayName||"Context")+".Consumer";case v:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case w:return null!==(t=e.displayName||null)?t:C(e.type)||"Memo";case x:t=e._payload,e=e._init;try{return C(e(t))}catch(n){}}return null}var P,_,T=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=Object.assign;function L(e){if(void 0===P)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);P=t&&t[1]||"",_=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+P+e+_}var z=!1;function O(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(l){r=l}(n=e())&&"function"===typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),l=a[0],i=a[1];if(l&&i){var s=l.split("\n"),c=i.split("\n");for(o=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;o<c.length&&!c[o].includes("DetermineComponentFrameRoot");)o++;if(r===s.length||o===c.length)for(r=s.length-1,o=c.length-1;1<=r&&0<=o&&s[r]!==c[o];)o--;for(;1<=r&&0<=o;r--,o--)if(s[r]!==c[o]){if(1!==r||1!==o)do{if(r--,0>--o||s[r]!==c[o]){var u="\n"+s[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=o);break}}}finally{z=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?L(n):""}function F(e){switch(e.tag){case 26:case 27:case 5:return L(e.type);case 16:return L("Lazy");case 13:return L("Suspense");case 19:return L("SuspenseList");case 0:case 15:return e=O(e.type,!1);case 11:return e=O(e.type.render,!1);case 1:return e=O(e.type,!0);default:return""}}function A(e){try{var t="";do{t+=F(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function M(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function D(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function R(e){if(M(e)!==e)throw Error(l(188))}function I(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=I(e)))return t;e=e.sibling}return null}var U=Array.isArray,B=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},W=[],H=-1;function q(e){return{current:e}}function Q(e){0>H||(e.current=W[H],W[H]=null,H--)}function K(e,t){H++,W[H]=e.current,e.current=t}var Y=q(null),G=q(null),X=q(null),J=q(null);function Z(e,t){switch(K(X,t),K(G,e),K(Y,null),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)&&(t=t.namespaceURI)?Gu(t):0;break;default:if(t=(e=8===e?t.parentNode:t).tagName,e=e.namespaceURI)t=Xu(e=Gu(e),t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Q(Y),K(Y,t)}function ee(){Q(Y),Q(G),Q(X)}function te(e){null!==e.memoizedState&&K(J,e);var t=Y.current,n=Xu(t,e.type);t!==n&&(K(G,e),K(Y,n))}function ne(e){G.current===e&&(Q(Y),Q(G)),J.current===e&&(Q(J),Ad._currentValue=V)}var re=Object.prototype.hasOwnProperty,oe=r.unstable_scheduleCallback,ae=r.unstable_cancelCallback,le=r.unstable_shouldYield,ie=r.unstable_requestPaint,se=r.unstable_now,ce=r.unstable_getCurrentPriorityLevel,ue=r.unstable_ImmediatePriority,de=r.unstable_UserBlockingPriority,fe=r.unstable_NormalPriority,pe=r.unstable_LowPriority,me=r.unstable_IdlePriority,he=r.log,ge=r.unstable_setDisableYieldValue,ve=null,be=null;function ye(e){if("function"===typeof he&&ge(e),be&&"function"===typeof be.setStrictMode)try{be.setStrictMode(ve,e)}catch(t){}}var we=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(xe(e)/ke|0)|0},xe=Math.log,ke=Math.LN2;var Se=128,Ne=4194304;function Ee(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function je(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,l=e.warmLanes;e=0!==e.finishedLanes;var i=134217727&n;return 0!==i?0!==(n=i&~o)?r=Ee(n):0!==(a&=i)?r=Ee(a):e||0!==(l=i&~l)&&(r=Ee(l)):0!==(i=n&~o)?r=Ee(i):0!==a?r=Ee(a):e||0!==(l=n&~l)&&(r=Ee(l)),0===r?0:0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(l=t&-t)||32===o&&0!==(4194176&l))?t:r}function Ce(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Pe(e,t){switch(e){case 1:case 2:case 4:case 8:return t+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function _e(){var e=Se;return 0===(4194176&(Se<<=1))&&(Se=128),e}function Te(){var e=Ne;return 0===(62914560&(Ne<<=1))&&(Ne=4194304),e}function $e(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Le(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ze(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-we(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194218&n}function Oe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-we(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Fe(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Ae(){var e=B.p;return 0!==e?e:void 0===(e=window.event)?32:Xd(e.type)}var Me=Math.random().toString(36).slice(2),De="__reactFiber$"+Me,Re="__reactProps$"+Me,Ie="__reactContainer$"+Me,Ue="__reactEvents$"+Me,Be="__reactListeners$"+Me,Ve="__reactHandles$"+Me,We="__reactResources$"+Me,He="__reactMarker$"+Me;function qe(e){delete e[De],delete e[Re],delete e[Ue],delete e[Be],delete e[Ve]}function Qe(e){var t=e[De];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ie]||n[De]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=sd(e);null!==e;){if(n=e[De])return n;e=sd(e)}return t}n=(e=n).parentNode}return null}function Ke(e){if(e=e[De]||e[Ie]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ye(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(l(33))}function Ge(e){var t=e[We];return t||(t=e[We]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Xe(e){e[He]=!0}var Je=new Set,Ze={};function et(e,t){tt(e,t),tt(e+"Capture",t)}function tt(e,t){for(Ze[e]=t,e=0;e<t.length;e++)Je.add(t[e])}var nt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),rt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ot={},at={};function lt(e,t,n){if(o=t,re.call(at,o)||!re.call(ot,o)&&(rt.test(o)?at[o]=!0:(ot[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function it(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function st(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var mt=/[\n"\\]/g;function ht(e){return e.replace(mt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function gt(e,t,n,r,o,a,l,i){e.name="",null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.type=l:e.removeAttribute("type"),null!=t?"number"===l?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=t?bt(e,l,ct(t)):null!=n?bt(e,l,ct(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=a&&(e.defaultChecked=!!a),null!=o&&(e.checked=o&&"function"!==typeof o&&"symbol"!==typeof o),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+ct(i):e.removeAttribute("name")}function vt(e,t,n,r,o,a,l,i){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:o)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.name=l)}function bt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function yt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(l(92));if(U(r)){if(1<r.length)throw Error(l(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Nt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(l(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&Nt(e,o,r)}else for(var a in t)t.hasOwnProperty(a)&&Nt(e,a,t[a])}function jt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ct=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _t(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Tt=null;function $t(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,zt=null;function Ot(e){var t=Ke(e);if(t&&(e=t.stateNode)){var n=e[Re]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[Re]||null;if(!o)throw Error(l(90));gt(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&yt(e,!!n.multiple,t,!1)}}}var Ft=!1;function At(e,t,n){if(Ft)return e(t,n);Ft=!0;try{return e(t)}finally{if(Ft=!1,(null!==Lt||null!==zt)&&(Dc(),Lt&&(t=Lt,e=zt,zt=Lt=null,Ot(t),e)))for(t=0;t<e.length;t++)Ot(e[t])}}function Mt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Re]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Dt=!1;if(nt)try{var Rt={};Object.defineProperty(Rt,"passive",{get:function(){Dt=!0}}),window.addEventListener("test",Rt,Rt),window.removeEventListener("test",Rt,Rt)}catch(kf){Dt=!1}var It=null,Ut=null,Bt=null;function Vt(){if(Bt)return Bt;var e,t,n=Ut,r=n.length,o="value"in It?It.value:It.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===o[a-t];t++);return Bt=o.slice(e,1<t?1-t:void 0)}function Wt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Ht(){return!0}function qt(){return!1}function Qt(e){function t(t,n,r,o,a){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(o):o[l]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?Ht:qt,this.isPropagationStopped=qt,this}return $(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Ht)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Ht)},persist:function(){},isPersistent:Ht}),t}var Kt,Yt,Gt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Jt=Qt(Xt),Zt=$({},Xt,{view:0,detail:0}),en=Qt(Zt),tn=$({},Zt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Gt&&(Gt&&"mousemove"===e.type?(Kt=e.screenX-Gt.screenX,Yt=e.screenY-Gt.screenY):Yt=Kt=0,Gt=e),Kt)},movementY:function(e){return"movementY"in e?e.movementY:Yt}}),nn=Qt(tn),rn=Qt($({},tn,{dataTransfer:0})),on=Qt($({},Zt,{relatedTarget:0})),an=Qt($({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Qt($({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),sn=Qt($({},Xt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},un={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=dn[e])&&!!t[e]}function pn(){return fn}var mn=Qt($({},Zt,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Wt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?un[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Wt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Wt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),hn=Qt($({},tn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),gn=Qt($({},Zt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),vn=Qt($({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Qt($({},tn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),yn=Qt($({},Xt,{newState:0,oldState:0})),wn=[9,13,27,32],xn=nt&&"CompositionEvent"in window,kn=null;nt&&"documentMode"in document&&(kn=document.documentMode);var Sn=nt&&"TextEvent"in window&&!kn,Nn=nt&&(!xn||kn&&8<kn&&11>=kn),En=String.fromCharCode(32),jn=!1;function Cn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var _n=!1;var Tn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Tn[e.type]:"textarea"===t}function Ln(e,t,n,r){Lt?zt?zt.push(r):zt=[r]:Lt=r,0<(t=Au(t,"onChange")).length&&(n=new Jt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zn=null,On=null;function Fn(e){Pu(e,0)}function An(e){if(ft(Ye(e)))return e}function Mn(e,t){if("change"===e)return t}var Dn=!1;if(nt){var Rn;if(nt){var In="oninput"in document;if(!In){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),In="function"===typeof Un.oninput}Rn=In}else Rn=!1;Dn=Rn&&(!document.documentMode||9<document.documentMode)}function Bn(){zn&&(zn.detachEvent("onpropertychange",Vn),On=zn=null)}function Vn(e){if("value"===e.propertyName&&An(On)){var t=[];Ln(t,On,e,$t(e)),At(Fn,t)}}function Wn(e,t,n){"focusin"===e?(Bn(),On=n,(zn=t).attachEvent("onpropertychange",Vn)):"focusout"===e&&Bn()}function Hn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return An(On)}function qn(e,t){if("click"===e)return An(t)}function Qn(e,t){if("input"===e||"change"===e)return An(t)}var Kn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Yn(e,t){if(Kn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!re.call(t,o)||!Kn(e[o],t[o]))return!1}return!0}function Gn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Gn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Gn(r)}}function Jn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Jn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Zn(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function er(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function tr(e,t){var n=Zn(t);t=e.focusedElem;var r=e.selectionRange;if(n!==t&&t&&t.ownerDocument&&Jn(t.ownerDocument.documentElement,t)){if(null!==r&&er(t))if(e=r.start,void 0===(n=r.end)&&(n=e),"selectionStart"in t)t.selectionStart=e,t.selectionEnd=Math.min(n,t.value.length);else if((n=(e=t.ownerDocument||document)&&e.defaultView||window).getSelection){n=n.getSelection();var o=t.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!n.extend&&a>r&&(o=r,r=a,a=o),o=Xn(t,a);var l=Xn(t,r);o&&l&&(1!==n.rangeCount||n.anchorNode!==o.node||n.anchorOffset!==o.offset||n.focusNode!==l.node||n.focusOffset!==l.offset)&&((e=e.createRange()).setStart(o.node,o.offset),n.removeAllRanges(),a>r?(n.addRange(e),n.extend(l.node,l.offset)):(e.setEnd(l.node,l.offset),n.addRange(e)))}for(e=[],n=t;n=n.parentNode;)1===n.nodeType&&e.push({element:n,left:n.scrollLeft,top:n.scrollTop});for("function"===typeof t.focus&&t.focus(),t=0;t<e.length;t++)(n=e[t]).element.scrollLeft=n.left,n.element.scrollTop=n.top}}var nr=nt&&"documentMode"in document&&11>=document.documentMode,rr=null,or=null,ar=null,lr=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&er(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Yn(ar,r)||(ar=r,0<(r=Au(or,"onSelect")).length&&(t=new Jt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}nt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var pr=fr("animationend"),mr=fr("animationiteration"),hr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),br=fr("transitioncancel"),yr=fr("transitionend"),wr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function kr(e,t){wr.set(e,t),et(t,[e])}var Sr=[],Nr=0,Er=0;function jr(){for(var e=Nr,t=Er=Nr=0;t<e;){var n=Sr[t];Sr[t++]=null;var r=Sr[t];Sr[t++]=null;var o=Sr[t];Sr[t++]=null;var a=Sr[t];if(Sr[t++]=null,null!==r&&null!==o){var l=r.pending;null===l?o.next=o:(o.next=l.next,l.next=o),r.pending=o}0!==a&&Tr(n,o,a)}}function Cr(e,t,n,r){Sr[Nr++]=e,Sr[Nr++]=t,Sr[Nr++]=n,Sr[Nr++]=r,Er|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return Cr(e,t,n,r),$r(e)}function _r(e,t){return Cr(e,null,null,t),$r(e)}function Tr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(o=!0)),e=a,a=a.return;o&&null!==t&&3===e.tag&&(a=e.stateNode,o=31-we(n),null===(e=(a=a.hiddenUpdates)[o])?a[o]=[t]:e.push(t),t.lane=536870912|n)}function $r(e){if(50<Pc)throw Pc=0,_c=null,Error(l(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Lr={},zr=new WeakMap;function Or(e,t){if("object"===typeof e&&null!==e){var n=zr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:A(t)},zr.set(e,t),t)}return{value:e,source:t,stack:A(t)}}var Fr=[],Ar=0,Mr=null,Dr=0,Rr=[],Ir=0,Ur=null,Br=1,Vr="";function Wr(e,t){Fr[Ar++]=Dr,Fr[Ar++]=Mr,Mr=e,Dr=t}function Hr(e,t,n){Rr[Ir++]=Br,Rr[Ir++]=Vr,Rr[Ir++]=Ur,Ur=e;var r=Br;e=Vr;var o=32-we(r)-1;r&=~(1<<o),n+=1;var a=32-we(t)+o;if(30<a){var l=o-o%5;a=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Br=1<<32-we(t)+o|n<<o|r,Vr=a+e}else Br=1<<a|n<<o|r,Vr=e}function qr(e){null!==e.return&&(Wr(e,1),Hr(e,1,0))}function Qr(e){for(;e===Mr;)Mr=Fr[--Ar],Fr[Ar]=null,Dr=Fr[--Ar],Fr[Ar]=null;for(;e===Ur;)Ur=Rr[--Ir],Rr[Ir]=null,Vr=Rr[--Ir],Rr[Ir]=null,Br=Rr[--Ir],Rr[Ir]=null}var Kr=null,Yr=null,Gr=!1,Xr=null,Jr=!1,Zr=Error(l(519));function eo(e){throw ao(Or(Error(l(418,"")),e)),Zr}function to(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[De]=e,t[Re]=r,n){case"dialog":_u("cancel",t),_u("close",t);break;case"iframe":case"object":case"embed":_u("load",t);break;case"video":case"audio":for(n=0;n<ju.length;n++)_u(ju[n],t);break;case"source":_u("error",t);break;case"img":case"image":case"link":_u("error",t),_u("load",t);break;case"details":_u("toggle",t);break;case"input":_u("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":_u("invalid",t);break;case"textarea":_u("invalid",t),xt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Bu(t.textContent,n)?(null!=r.popover&&(_u("beforetoggle",t),_u("toggle",t)),null!=r.onScroll&&_u("scroll",t),null!=r.onScrollEnd&&_u("scrollend",t),null!=r.onClick&&(t.onclick=Vu),t=!0):t=!1,t||eo(e)}function no(e){for(Kr=e.return;Kr;)switch(Kr.tag){case 3:case 27:return void(Jr=!0);case 5:case 13:return void(Jr=!1);default:Kr=Kr.return}}function ro(e){if(e!==Kr)return!1;if(!Gr)return no(e),Gr=!0,!1;var t,n=!1;if((t=3!==e.tag&&27!==e.tag)&&((t=5===e.tag)&&(t=!("form"!==(t=e.type)&&"button"!==t)||Ju(e.type,e.memoizedProps)),t=!t),t&&(n=!0),n&&Yr&&eo(e),no(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){Yr=id(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}Yr=null}}else Yr=Kr?id(e.stateNode.nextSibling):null;return!0}function oo(){Yr=Kr=null,Gr=!1}function ao(e){null===Xr?Xr=[e]:Xr.push(e)}var lo=Error(l(460)),io=Error(l(474)),so={then:function(){}};function co(e){return"fulfilled"===(e=e.status)||"rejected"===e}function uo(){}function fo(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(uo,uo),t=n),t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===lo)throw Error(l(483));throw e;default:if("string"===typeof t.status)t.then(uo,uo);else{if(null!==(e=tc)&&100<e.shellSuspendCounter)throw Error(l(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===lo)throw Error(l(483));throw e}throw po=t,lo}}var po=null;function mo(){if(null===po)throw Error(l(459));var e=po;return po=null,e}var ho=null,go=0;function vo(e){var t=go;return go+=1,null===ho&&(ho=[]),fo(ho,e,t)}function bo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function yo(e,t){if(t.$$typeof===s)throw Error(l(525));throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function wo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=Ms(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=33554434,n):r:(t.flags|=33554434,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=33554434),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Bs(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n,r){var a=n.type;return a===d?m(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===x&&wo(a)===t.type)?(bo(t=o(t,n.props),n),t.return=e,t):(bo(t=Rs(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function p(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vs(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function m(e,t,n,r,a){return null===t||7!==t.tag?((t=Is(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function h(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Bs(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case c:return bo(n=Rs(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case u:return(t=Vs(t,e.mode,n)).return=e,t;case x:return h(e,t=(0,t._init)(t._payload),n)}if(U(t)||E(t))return(t=Is(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return h(e,vo(t),n);if(t.$$typeof===g)return h(e,ji(e,t),n);yo(e,t)}return null}function v(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case c:return n.key===o?f(e,t,n,r):null;case u:return n.key===o?p(e,t,n,r):null;case x:return v(e,t,n=(o=n._init)(n._payload),r)}if(U(n)||E(n))return null!==o?null:m(e,t,n,r,null);if("function"===typeof n.then)return v(e,t,vo(n),r);if(n.$$typeof===g)return v(e,t,ji(e,n),r);yo(e,n)}return null}function b(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case c:return f(t,e=e.get(null===r.key?n:r.key)||null,r,o);case u:return p(t,e=e.get(null===r.key?n:r.key)||null,r,o);case x:return b(e,t,n,r=(0,r._init)(r._payload),o)}if(U(r)||E(r))return m(t,e=e.get(n)||null,r,o,null);if("function"===typeof r.then)return b(e,t,n,vo(r),o);if(r.$$typeof===g)return b(e,t,n,ji(t,r),o);yo(t,r)}return null}function y(s,f,p,m){if("object"===typeof p&&null!==p&&p.type===d&&null===p.key&&(p=p.props.children),"object"===typeof p&&null!==p){switch(p.$$typeof){case c:e:{for(var w=p.key;null!==f;){if(f.key===w){if((w=p.type)===d){if(7===f.tag){n(s,f.sibling),(m=o(f,p.props.children)).return=s,s=m;break e}}else if(f.elementType===w||"object"===typeof w&&null!==w&&w.$$typeof===x&&wo(w)===f.type){n(s,f.sibling),bo(m=o(f,p.props),p),m.return=s,s=m;break e}n(s,f);break}t(s,f),f=f.sibling}p.type===d?((m=Is(p.props.children,s.mode,m,p.key)).return=s,s=m):(bo(m=Rs(p.type,p.key,p.props,null,s.mode,m),p),m.return=s,s=m)}return i(s);case u:e:{for(w=p.key;null!==f;){if(f.key===w){if(4===f.tag&&f.stateNode.containerInfo===p.containerInfo&&f.stateNode.implementation===p.implementation){n(s,f.sibling),(m=o(f,p.children||[])).return=s,s=m;break e}n(s,f);break}t(s,f),f=f.sibling}(m=Vs(p,s.mode,m)).return=s,s=m}return i(s);case x:return y(s,f,p=(w=p._init)(p._payload),m)}if(U(p))return function(o,l,i,s){for(var c=null,u=null,d=l,f=l=0,p=null;null!==d&&f<i.length;f++){d.index>f?(p=d,d=null):p=d.sibling;var m=v(o,d,i[f],s);if(null===m){null===d&&(d=p);break}e&&d&&null===m.alternate&&t(o,d),l=a(m,l,f),null===u?c=m:u.sibling=m,u=m,d=p}if(f===i.length)return n(o,d),Gr&&Wr(o,f),c;if(null===d){for(;f<i.length;f++)null!==(d=h(o,i[f],s))&&(l=a(d,l,f),null===u?c=d:u.sibling=d,u=d);return Gr&&Wr(o,f),c}for(d=r(d);f<i.length;f++)null!==(p=b(d,o,f,i[f],s))&&(e&&null!==p.alternate&&d.delete(null===p.key?f:p.key),l=a(p,l,f),null===u?c=p:u.sibling=p,u=p);return e&&d.forEach((function(e){return t(o,e)})),Gr&&Wr(o,f),c}(s,f,p,m);if(E(p)){if("function"!==typeof(w=E(p)))throw Error(l(150));return function(o,i,s,c){if(null==s)throw Error(l(151));for(var u=null,d=null,f=i,p=i=0,m=null,g=s.next();null!==f&&!g.done;p++,g=s.next()){f.index>p?(m=f,f=null):m=f.sibling;var y=v(o,f,g.value,c);if(null===y){null===f&&(f=m);break}e&&f&&null===y.alternate&&t(o,f),i=a(y,i,p),null===d?u=y:d.sibling=y,d=y,f=m}if(g.done)return n(o,f),Gr&&Wr(o,p),u;if(null===f){for(;!g.done;p++,g=s.next())null!==(g=h(o,g.value,c))&&(i=a(g,i,p),null===d?u=g:d.sibling=g,d=g);return Gr&&Wr(o,p),u}for(f=r(f);!g.done;p++,g=s.next())null!==(g=b(f,o,p,g.value,c))&&(e&&null!==g.alternate&&f.delete(null===g.key?p:g.key),i=a(g,i,p),null===d?u=g:d.sibling=g,d=g);return e&&f.forEach((function(e){return t(o,e)})),Gr&&Wr(o,p),u}(s,f,p=w.call(p),m)}if("function"===typeof p.then)return y(s,f,vo(p),m);if(p.$$typeof===g)return y(s,f,ji(s,p),m);yo(s,p)}return"string"===typeof p&&""!==p||"number"===typeof p||"bigint"===typeof p?(p=""+p,null!==f&&6===f.tag?(n(s,f.sibling),(m=o(f,p)).return=s,s=m):(n(s,f),(m=Bs(p,s.mode,m)).return=s,s=m),i(s)):n(s,f)}return function(e,t,n,r){try{go=0;var o=y(e,t,n,r);return ho=null,o}catch(l){if(l===lo)throw l;var a=Fs(29,l,null,e.mode);return a.lanes=r,a.return=e,a}}}var ko=xo(!0),So=xo(!1),No=q(null),Eo=q(0);function jo(e,t){K(Eo,e=cc),K(No,t),cc=e|t.baseLanes}function Co(){K(Eo,cc),K(No,No.current)}function Po(){cc=Eo.current,Q(No),Q(Eo)}var _o=q(null),To=null;function $o(e){var t=e.alternate;K(Fo,1&Fo.current),K(_o,e),null===To&&(null===t||null!==No.current||null!==t.memoizedState)&&(To=e)}function Lo(e){if(22===e.tag){if(K(Fo,Fo.current),K(_o,e),null===To){var t=e.alternate;null!==t&&null!==t.memoizedState&&(To=e)}}else zo()}function zo(){K(Fo,Fo.current),K(_o,_o.current)}function Oo(e){Q(_o),To===e&&(To=null),Q(Fo)}var Fo=q(0);function Ao(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Mo="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Do=r.unstable_scheduleCallback,Ro=r.unstable_NormalPriority,Io={$$typeof:g,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uo(){return{controller:new Mo,data:new Map,refCount:0}}function Bo(e){e.refCount--,0===e.refCount&&Do(Ro,(function(){e.controller.abort()}))}var Vo=null,Wo=0,Ho=0,qo=null;function Qo(){if(0===--Wo&&null!==Vo){null!==qo&&(qo.status="fulfilled");var e=Vo;Vo=null,Ho=0,qo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ko=T.S;T.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Vo){var n=Vo=[];Wo=0,Ho=xu(),qo={status:"pending",value:void 0,then:function(e){n.push(e)}}}Wo++,t.then(Qo,Qo)}(0,t),null!==Ko&&Ko(e,t)};var Yo=q(null);function Go(){var e=Yo.current;return null!==e?e:tc.pooledCache}function Xo(e,t){K(Yo,null===t?Yo.current:t.pool)}function Jo(){var e=Go();return null===e?null:{parent:Io._currentValue,pool:e}}var Zo=0,ea=null,ta=null,na=null,ra=!1,oa=!1,aa=!1,la=0,ia=0,sa=null,ca=0;function ua(){throw Error(l(321))}function da(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Kn(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,o,a){return Zo=a,ea=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=null===e||null===e.memoizedState?jl:Cl,aa=!1,a=n(r,o),aa=!1,oa&&(a=ma(t,n,r,o)),pa(e),a}function pa(e){T.H=El;var t=null!==ta&&null!==ta.next;if(Zo=0,na=ta=ea=null,ra=!1,ia=0,sa=null,t)throw Error(l(300));null===e||Wl||null!==(e=e.dependencies)&&Si(e)&&(Wl=!0)}function ma(e,t,n,r){ea=e;var o=0;do{if(oa&&(sa=null),ia=0,oa=!1,25<=o)throw Error(l(301));if(o+=1,na=ta=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}T.H=Pl,a=t(n,r)}while(oa);return a}function ha(){var e=T.H,t=e.useState()[0];return t="function"===typeof t.then?xa(t):t,e=e.useState()[0],(null!==ta?ta.memoizedState:null)!==e&&(ea.flags|=1024),t}function ga(){var e=0!==la;return la=0,e}function va(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function ba(e){if(ra){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ra=!1}Zo=0,na=ta=ea=null,oa=!1,ia=la=0,sa=null}function ya(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===na?ea.memoizedState=na=e:na=na.next=e,na}function wa(){if(null===ta){var e=ea.alternate;e=null!==e?e.memoizedState:null}else e=ta.next;var t=null===na?ea.memoizedState:na.next;if(null!==t)na=t,ta=e;else{if(null===e){if(null===ea.alternate)throw Error(l(467));throw Error(l(310))}e={memoizedState:(ta=e).memoizedState,baseState:ta.baseState,baseQueue:ta.baseQueue,queue:ta.queue,next:null},null===na?ea.memoizedState=na=e:na=na.next=e}return na}function xa(e){var t=ia;return ia+=1,null===sa&&(sa=[]),e=fo(sa,e,t),t=ea,null===(null===na?t.memoizedState:na.next)&&(t=t.alternate,T.H=null===t||null===t.memoizedState?jl:Cl),e}function ka(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return xa(e);if(e.$$typeof===g)return Ei(e)}throw Error(l(438,String(e)))}function Sa(e){var t=null,n=ea.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=ea.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=S;return t.index++,n}function Na(e,t){return"function"===typeof t?t(e):t}function Ea(e){return ja(wa(),ta,e)}function ja(e,t,n){var r=e.queue;if(null===r)throw Error(l(311));r.lastRenderedReducer=n;var o=e.baseQueue,a=r.pending;if(null!==a){if(null!==o){var i=o.next;o.next=a.next,a.next=i}t.baseQueue=o=a,r.pending=null}if(a=e.baseState,null===o)e.memoizedState=a;else{var s=i=null,c=null,u=t=o.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(rc&f)===f:(Zo&f)===f){var p=u.revertLane;if(0===p)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===Ho&&(d=!0);else{if((Zo&p)===p){u=u.next,p===Ho&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=f,i=a):c=c.next=f,ea.lanes|=p,dc|=p}f=u.action,aa&&n(a,f),a=u.hasEagerState?u.eagerState:n(a,f)}else p={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=p,i=a):c=c.next=p,ea.lanes|=f,dc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?i=a:c.next=s,!Kn(a,e.memoizedState)&&(Wl=!0,d&&null!==(n=qo)))throw n;e.memoizedState=a,e.baseState=i,e.baseQueue=c,r.lastRenderedState=a}return null===o&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ca(e){var t=wa(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var i=o=o.next;do{a=e(a,i.action),i=i.next}while(i!==o);Kn(a,t.memoizedState)||(Wl=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Pa(e,t,n){var r=ea,o=wa(),a=Gr;if(a){if(void 0===n)throw Error(l(407));n=n()}else n=t();var i=!Kn((ta||o).memoizedState,n);if(i&&(o.memoizedState=n,Wl=!0),o=o.queue,Za($a.bind(null,r,o,e),[e]),o.getSnapshot!==t||i||null!==na&&1&na.memoizedState.tag){if(r.flags|=2048,Ka(9,Ta.bind(null,r,o,n,t),{destroy:void 0},null),null===tc)throw Error(l(349));a||0!==(60&Zo)||_a(r,t,n)}return n}function _a(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ea.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ta(e,t,n,r){t.value=n,t.getSnapshot=r,La(t)&&za(e)}function $a(e,t,n){return n((function(){La(t)&&za(e)}))}function La(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Kn(e,n)}catch(r){return!0}}function za(e){var t=_r(e,2);null!==t&&Lc(t,e,2)}function Oa(e){var t=ya();if("function"===typeof e){var n=e;if(e=n(),aa){ye(!0);try{n()}finally{ye(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:e},t}function Fa(e,t,n,r){return e.baseState=n,ja(e,ta,"function"===typeof r?r:Na)}function Aa(e,t,n,r,o){if(kl(e))throw Error(l(485));if(null!==(e=t.action)){var a={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==T.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,Ma(t,a)):(a.next=n.next,t.pending=n.next=a)}}function Ma(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var a=T.T,l={};T.T=l;try{var i=n(o,r),s=T.S;null!==s&&s(l,i),Da(e,t,i)}catch(c){Ia(e,t,c)}finally{T.T=a}}else try{Da(e,t,a=n(o,r))}catch(u){Ia(e,t,u)}}function Da(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then((function(n){Ra(e,t,n)}),(function(n){return Ia(e,t,n)})):Ra(e,t,n)}function Ra(e,t,n){t.status="fulfilled",t.value=n,Ua(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,Ma(e,n)))}function Ia(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,Ua(t),t=t.next}while(t!==r)}e.action=null}function Ua(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ba(e,t){return t}function Va(e,t){if(Gr){var n=tc.formState;if(null!==n){e:{var r=ea;if(Gr){if(Yr){t:{for(var o=Yr,a=Jr;8!==o.nodeType;){if(!a){o=null;break t}if(null===(o=id(o.nextSibling))){o=null;break t}}o="F!"===(a=o.data)||"F"===a?o:null}if(o){Yr=id(o.nextSibling),r="F!"===o.data;break e}}eo(r)}r=!1}r&&(t=n[0])}}return(n=ya()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:t},n.queue=r,n=yl.bind(null,ea,r),r.dispatch=n,r=Oa(!1),a=xl.bind(null,ea,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=ya()).queue=o,n=Aa.bind(null,ea,o,a,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function Wa(e){return Ha(wa(),ta,e)}function Ha(e,t,n){t=ja(e,t,Ba)[0],e=Ea(Na)[0],t="object"===typeof t&&null!==t&&"function"===typeof t.then?xa(t):t;var r=wa(),o=r.queue,a=o.dispatch;return n!==r.memoizedState&&(ea.flags|=2048,Ka(9,qa.bind(null,o,n),{destroy:void 0},null)),[t,a,e]}function qa(e,t){e.action=t}function Qa(e){var t=wa(),n=ta;if(null!==n)return Ha(t,n,e);wa(),t=t.memoizedState;var r=(n=wa()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function Ka(e,t,n,r){return e={tag:e,create:t,inst:n,deps:r,next:null},null===(t=ea.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ya(){return wa().memoizedState}function Ga(e,t,n,r){var o=ya();ea.flags|=e,o.memoizedState=Ka(1|t,n,{destroy:void 0},void 0===r?null:r)}function Xa(e,t,n,r){var o=wa();r=void 0===r?null:r;var a=o.memoizedState.inst;null!==ta&&null!==r&&da(r,ta.memoizedState.deps)?o.memoizedState=Ka(t,n,a,r):(ea.flags|=e,o.memoizedState=Ka(1|t,n,a,r))}function Ja(e,t){Ga(8390656,8,e,t)}function Za(e,t){Xa(2048,8,e,t)}function el(e,t){return Xa(4,2,e,t)}function tl(e,t){return Xa(4,4,e,t)}function nl(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function rl(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,Xa(4,4,nl.bind(null,t,e),n)}function ol(){}function al(e,t){var n=wa();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&da(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ll(e,t){var n=wa();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&da(t,r[1]))return r[0];if(r=e(),aa){ye(!0);try{e()}finally{ye(!1)}}return n.memoizedState=[r,t],r}function il(e,t,n){return void 0===n||0!==(1073741824&Zo)?e.memoizedState=t:(e.memoizedState=n,e=$c(),ea.lanes|=e,dc|=e,n)}function sl(e,t,n,r){return Kn(n,t)?n:null!==No.current?(e=il(e,n,r),Kn(e,t)||(Wl=!0),e):0===(42&Zo)?(Wl=!0,e.memoizedState=n):(e=$c(),ea.lanes|=e,dc|=e,t)}function cl(e,t,n,r,o){var a=B.p;B.p=0!==a&&8>a?a:8;var l=T.T,i={};T.T=i,xl(e,!1,t,n);try{var s=o(),c=T.S;if(null!==c&&c(i,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)wl(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then((function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)}),(function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)})),r}(s,r),Tc());else wl(e,t,r,Tc())}catch(u){wl(e,t,{then:function(){},status:"rejected",reason:u},Tc())}finally{B.p=a,T.T=l}}function ul(){}function dl(e,t,n,r){if(5!==e.tag)throw Error(l(476));var o=fl(e).queue;cl(e,o,t,V,null===n?ul:function(){return pl(e),n(r)})}function fl(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:V},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function pl(e){wl(e,fl(e).next.queue,{},Tc())}function ml(){return Ei(Ad)}function hl(){return wa().memoizedState}function gl(){return wa().memoizedState}function vl(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Tc(),r=Li(t,e=$i(n),n);return null!==r&&(Lc(r,t,n),zi(r,t,n)),t={cache:Uo()},void(e.payload=t)}t=t.return}}function bl(e,t,n){var r=Tc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},kl(e)?Sl(t,n):null!==(n=Pr(e,t,n,r))&&(Lc(n,e,r),Nl(n,t,r))}function yl(e,t,n){wl(e,t,n,Tc())}function wl(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(kl(e))Sl(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=a(l,n);if(o.hasEagerState=!0,o.eagerState=i,Kn(i,l))return Cr(e,t,o,0),null===tc&&jr(),!1}catch(s){}if(null!==(n=Pr(e,t,o,r)))return Lc(n,e,r),Nl(n,t,r),!0}return!1}function xl(e,t,n,r){if(r={lane:2,revertLane:xu(),action:r,hasEagerState:!1,eagerState:null,next:null},kl(e)){if(t)throw Error(l(479))}else null!==(t=Pr(e,n,r,2))&&Lc(t,e,2)}function kl(e){var t=e.alternate;return e===ea||null!==t&&t===ea}function Sl(e,t){oa=ra=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Nl(e,t,n){if(0!==(4194176&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}var El={readContext:Ei,use:ka,useCallback:ua,useContext:ua,useEffect:ua,useImperativeHandle:ua,useLayoutEffect:ua,useInsertionEffect:ua,useMemo:ua,useReducer:ua,useRef:ua,useState:ua,useDebugValue:ua,useDeferredValue:ua,useTransition:ua,useSyncExternalStore:ua,useId:ua};El.useCacheRefresh=ua,El.useMemoCache=ua,El.useHostTransitionStatus=ua,El.useFormState=ua,El.useActionState=ua,El.useOptimistic=ua;var jl={readContext:Ei,use:ka,useCallback:function(e,t){return ya().memoizedState=[e,void 0===t?null:t],e},useContext:Ei,useEffect:Ja,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,Ga(4194308,4,nl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ga(4194308,4,e,t)},useInsertionEffect:function(e,t){Ga(4,2,e,t)},useMemo:function(e,t){var n=ya();t=void 0===t?null:t;var r=e();if(aa){ye(!0);try{e()}finally{ye(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=ya();if(void 0!==n){var o=n(t);if(aa){ye(!0);try{n(t)}finally{ye(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=bl.bind(null,ea,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ya().memoizedState=e},useState:function(e){var t=(e=Oa(e)).queue,n=yl.bind(null,ea,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ol,useDeferredValue:function(e,t){return il(ya(),e,t)},useTransition:function(){var e=Oa(!1);return e=cl.bind(null,ea,e.queue,!0,!1),ya().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=ea,o=ya();if(Gr){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===tc)throw Error(l(349));0!==(60&rc)||_a(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,Ja($a.bind(null,r,a,e),[e]),r.flags|=2048,Ka(9,Ta.bind(null,r,a,n,t),{destroy:void 0},null),n},useId:function(){var e=ya(),t=tc.identifierPrefix;if(Gr){var n=Vr;t=":"+t+"R"+(n=(Br&~(1<<32-we(Br)-1)).toString(32)+n),0<(n=la++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ca++).toString(32)+":";return e.memoizedState=t},useCacheRefresh:function(){return ya().memoizedState=vl.bind(null,ea)}};jl.useMemoCache=Sa,jl.useHostTransitionStatus=ml,jl.useFormState=Va,jl.useActionState=Va,jl.useOptimistic=function(e){var t=ya();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=xl.bind(null,ea,!0,n),n.dispatch=t,[e,t]};var Cl={readContext:Ei,use:ka,useCallback:al,useContext:Ei,useEffect:Za,useImperativeHandle:rl,useInsertionEffect:el,useLayoutEffect:tl,useMemo:ll,useReducer:Ea,useRef:Ya,useState:function(){return Ea(Na)},useDebugValue:ol,useDeferredValue:function(e,t){return sl(wa(),ta.memoizedState,e,t)},useTransition:function(){var e=Ea(Na)[0],t=wa().memoizedState;return["boolean"===typeof e?e:xa(e),t]},useSyncExternalStore:Pa,useId:hl};Cl.useCacheRefresh=gl,Cl.useMemoCache=Sa,Cl.useHostTransitionStatus=ml,Cl.useFormState=Wa,Cl.useActionState=Wa,Cl.useOptimistic=function(e,t){return Fa(wa(),0,e,t)};var Pl={readContext:Ei,use:ka,useCallback:al,useContext:Ei,useEffect:Za,useImperativeHandle:rl,useInsertionEffect:el,useLayoutEffect:tl,useMemo:ll,useReducer:Ca,useRef:Ya,useState:function(){return Ca(Na)},useDebugValue:ol,useDeferredValue:function(e,t){var n=wa();return null===ta?il(n,e,t):sl(n,ta.memoizedState,e,t)},useTransition:function(){var e=Ca(Na)[0],t=wa().memoizedState;return["boolean"===typeof e?e:xa(e),t]},useSyncExternalStore:Pa,useId:hl};function _l(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:$({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}Pl.useCacheRefresh=gl,Pl.useMemoCache=Sa,Pl.useHostTransitionStatus=ml,Pl.useFormState=Qa,Pl.useActionState=Qa,Pl.useOptimistic=function(e,t){var n=wa();return null!==ta?Fa(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])};var Tl={isMounted:function(e){return!!(e=e._reactInternals)&&M(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Tc(),o=$i(r);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Li(e,o,r))&&(Lc(t,e,r),zi(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Tc(),o=$i(r);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Li(e,o,r))&&(Lc(t,e,r),zi(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Tc(),r=$i(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=Li(e,r,n))&&(Lc(t,e,n),zi(t,e,n))}};function $l(e,t,n,r,o,a,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,l):!t.prototype||!t.prototype.isPureReactComponent||(!Yn(n,r)||!Yn(o,a))}function Ll(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Tl.enqueueReplaceState(t,t.state,null)}function zl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=$({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var Ol="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function Fl(e){Ol(e)}function Al(e){console.error(e)}function Ml(e){Ol(e)}function Dl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function Rl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Il(e,t,n){return(n=$i(n)).tag=3,n.payload={element:null},n.callback=function(){Dl(e,t)},n}function Ul(e){return(e=$i(e)).tag=3,e}function Bl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"===typeof o){var a=r.value;e.payload=function(){return o(a)},e.callback=function(){Rl(t,n,r)}}var l=n.stateNode;null!==l&&"function"===typeof l.componentDidCatch&&(e.callback=function(){Rl(t,n,r),"function"!==typeof o&&(null===kc?kc=new Set([this]):kc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Vl=Error(l(461)),Wl=!1;function Hl(e,t,n,r){t.child=null===e?So(t,null,n,r):ko(t,e.child,n,r)}function ql(e,t,n,r,o){n=n.render;var a=t.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return Ni(t),r=fa(e,t,n,l,a,o),i=ga(),null===e||Wl?(Gr&&i&&qr(t),t.flags|=1,Hl(e,t,r,o),t.child):(va(e,t,o),fi(e,t,o))}function Ql(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||As(a)||void 0!==a.defaultProps||null!==n.compare?((e=Rs(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Kl(e,t,a,r,o))}if(a=e.child,!pi(e,o)){var l=a.memoizedProps;if((n=null!==(n=n.compare)?n:Yn)(l,r)&&e.ref===t.ref)return fi(e,t,o)}return t.flags|=1,(e=Ms(a,r)).ref=t.ref,e.return=t,t.child=e}function Kl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(Yn(a,r)&&e.ref===t.ref){if(Wl=!1,t.pendingProps=r=a,!pi(e,o))return t.lanes=e.lanes,fi(e,t,o);0!==(131072&e.flags)&&(Wl=!0)}}return Jl(e,t,n,r,o)}function Yl(e,t,n){var r=t.pendingProps,o=r.children,a=0!==(2&t.stateNode._pendingVisibility),l=null!==e?e.memoizedState:null;if(Xl(e,t),"hidden"===r.mode||a){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(o=t.child=e.child,a=0;null!==o;)a=a|o.lanes|o.childLanes,o=o.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return Gl(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Gl(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Xo(0,null!==l?l.cachePool:null),null!==l?jo(t,l):Co(),Lo(t)}else null!==l?(Xo(0,l.cachePool),jo(t,l),zo(),t.memoizedState=null):(null!==e&&Xo(0,null),Co(),zo());return Hl(e,t,o,n),t.child}function Gl(e,t,n,r){var o=Go();return o=null===o?null:{parent:Io._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&Xo(0,null),Co(),Lo(t),null!==e&&ki(e,t,r,!0),null}function Xl(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=2097664);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(l(284));null!==e&&e.ref===n||(t.flags|=2097664)}}function Jl(e,t,n,r,o){return Ni(t),n=fa(e,t,n,r,void 0,o),r=ga(),null===e||Wl?(Gr&&r&&qr(t),t.flags|=1,Hl(e,t,n,o),t.child):(va(e,t,o),fi(e,t,o))}function Zl(e,t,n,r,o,a){return Ni(t),t.updateQueue=null,n=ma(t,r,n,o),pa(e),r=ga(),null===e||Wl?(Gr&&r&&qr(t),t.flags|=1,Hl(e,t,n,a),t.child):(va(e,t,a),fi(e,t,a))}function ei(e,t,n,r,o){if(Ni(t),null===t.stateNode){var a=Lr,l=n.contextType;"object"===typeof l&&null!==l&&(a=Ei(l)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=Tl,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},_i(t),l=n.contextType,a.context="object"===typeof l&&null!==l?Ei(l):Lr,a.state=t.memoizedState,"function"===typeof(l=n.getDerivedStateFromProps)&&(_l(t,n,l,r),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(l=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),l!==a.state&&Tl.enqueueReplaceState(a,a.state,null),Mi(t,r,a,o),Ai(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var i=t.memoizedProps,s=zl(n,i);a.props=s;var c=a.context,u=n.contextType;l=Lr,"object"===typeof u&&null!==u&&(l=Ei(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,i=t.pendingProps!==i,u||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(i||c!==l)&&Ll(t,a,r,l),Pi=!1;var f=t.memoizedState;a.state=f,Mi(t,r,a,o),Ai(),c=t.memoizedState,i||f!==c||Pi?("function"===typeof d&&(_l(t,n,d,r),c=t.memoizedState),(s=Pi||$l(t,n,s,r,f,c,l))?(u||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=l,r=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Ti(e,t),u=zl(n,l=t.memoizedProps),a.props=u,d=t.pendingProps,f=a.context,c=n.contextType,s=Lr,"object"===typeof c&&null!==c&&(s=Ei(c)),(c="function"===typeof(i=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==d||f!==s)&&Ll(t,a,r,s),Pi=!1,f=t.memoizedState,a.state=f,Mi(t,r,a,o),Ai();var p=t.memoizedState;l!==d||f!==p||Pi||null!==e&&null!==e.dependencies&&Si(e.dependencies)?("function"===typeof i&&(_l(t,n,i,r),p=t.memoizedState),(u=Pi||$l(t,n,u,r,f,p,s)||null!==e&&null!==e.dependencies&&Si(e.dependencies))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,r=u):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,Xl(e,t),r=0!==(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=ko(t,e.child,null,o),t.child=ko(t,null,n,o)):Hl(e,t,n,o),t.memoizedState=a.state,e=t.child):e=fi(e,t,o),e}function ti(e,t,n,r){return oo(),t.flags|=256,Hl(e,t,n,r),t.child}var ni={dehydrated:null,treeContext:null,retryLane:0};function ri(e){return{baseLanes:e,cachePool:Jo()}}function oi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=mc),e}function ai(e,t,n){var r,o=t.pendingProps,a=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&Fo.current)),r&&(a=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(Gr){if(a?$o(t):zo(),Gr){var s,c=Yr;if(s=c){e:{for(s=c,c=Jr;8!==s.nodeType;){if(!c){c=null;break e}if(null===(s=id(s.nextSibling))){c=null;break e}}c=s}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Ur?{id:Br,overflow:Vr}:null,retryLane:536870912},(s=Fs(18,null,null,0)).stateNode=c,s.return=t,t.child=s,Kr=t,Yr=null,s=!0):s=!1}s||eo(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return"$!"===c.data?t.lanes=16:t.lanes=536870912,null;Oo(t)}return c=o.children,o=o.fallback,a?(zo(),c=ii({mode:"hidden",children:c},a=t.mode),o=Is(o,a,n,null),c.return=t,o.return=t,c.sibling=o,t.child=c,(a=t.child).memoizedState=ri(n),a.childLanes=oi(e,r,n),t.memoizedState=ni,o):($o(t),li(t,c))}if(null!==(s=e.memoizedState)&&null!==(c=s.dehydrated)){if(i)256&t.flags?($o(t),t.flags&=-257,t=si(e,t,n)):null!==t.memoizedState?(zo(),t.child=e.child,t.flags|=128,t=null):(zo(),a=o.fallback,c=t.mode,o=ii({mode:"visible",children:o.children},c),(a=Is(a,c,n,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,ko(t,e.child,null,n),(o=t.child).memoizedState=ri(n),o.childLanes=oi(e,r,n),t.memoizedState=ni,t=a);else if($o(t),"$!"===c.data){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(o=Error(l(419))).stack="",o.digest=r,ao({value:o,source:null,stack:null}),t=si(e,t,n)}else if(Wl||ki(e,t,n,!1),r=0!==(n&e.childLanes),Wl||r){if(null!==(r=tc)){if(0!==(42&(o=n&-n)))o=1;else switch(o){case 2:o=1;break;case 8:o=4;break;case 32:o=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:o=64;break;case 268435456:o=134217728;break;default:o=0}if(0!==(o=0!==(o&(r.suspendedLanes|n))?0:o)&&o!==s.retryLane)throw s.retryLane=o,_r(e,o),Lc(r,e,o),Vl}"$?"===c.data||Wc(),t=si(e,t,n)}else"$?"===c.data?(t.flags|=128,t.child=e.child,t=iu.bind(null,e),c._reactRetry=t,t=null):(e=s.treeContext,Yr=id(c.nextSibling),Kr=t,Gr=!0,Xr=null,Jr=!1,null!==e&&(Rr[Ir++]=Br,Rr[Ir++]=Vr,Rr[Ir++]=Ur,Br=e.id,Vr=e.overflow,Ur=t),(t=li(t,o.children)).flags|=4096);return t}return a?(zo(),a=o.fallback,c=t.mode,u=(s=e.child).sibling,(o=Ms(s,{mode:"hidden",children:o.children})).subtreeFlags=31457280&s.subtreeFlags,null!==u?a=Ms(u,a):(a=Is(a,c,n,null)).flags|=2,a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,null===(c=e.child.memoizedState)?c=ri(n):(null!==(s=c.cachePool)?(u=Io._currentValue,s=s.parent!==u?{parent:u,pool:u}:s):s=Jo(),c={baseLanes:c.baseLanes|n,cachePool:s}),a.memoizedState=c,a.childLanes=oi(e,r,n),t.memoizedState=ni,o):($o(t),e=(n=e.child).sibling,(n=Ms(n,{mode:"visible",children:o.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function li(e,t){return(t=ii({mode:"visible",children:t},e.mode)).return=e,e.child=t}function ii(e,t){return Us(e,t,0,null)}function si(e,t,n){return ko(t,e.child,null,n),(e=li(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function ci(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wi(e.return,t,n)}function ui(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function di(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(Hl(e,t,r.children,n),0!==(2&(r=Fo.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&ci(e,n,t);else if(19===e.tag)ci(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(K(Fo,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===Ao(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ui(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===Ao(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ui(t,!0,n,null,a);break;case"together":ui(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function fi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),dc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ki(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Ms(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ms(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function pi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Si(e))}function mi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Wl=!0;else{if(!pi(e,n)&&0===(128&t.flags))return Wl=!1,function(e,t,n){switch(t.tag){case 3:Z(t,t.stateNode.containerInfo),bi(t,Io,e.memoizedState.cache),oo();break;case 27:case 5:te(t);break;case 4:Z(t,t.stateNode.containerInfo);break;case 10:bi(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?($o(t),t.flags|=128,null):0!==(n&t.child.childLanes)?ai(e,t,n):($o(t),null!==(e=fi(e,t,n))?e.sibling:null);$o(t);break;case 19:var o=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(ki(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return di(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),K(Fo,Fo.current),r)break;return null;case 22:case 23:return t.lanes=0,Yl(e,t,n);case 24:bi(t,Io,e.memoizedState.cache)}return fi(e,t,n)}(e,t,n);Wl=0!==(131072&e.flags)}else Wl=!1,Gr&&0!==(1048576&t.flags)&&Hr(t,Dr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((o=r.$$typeof)===v){t.tag=11,t=ql(null,t,r,e,n);break e}if(o===w){t.tag=14,t=Ql(null,t,r,e,n);break e}}throw t=C(r)||r,Error(l(306,t,""))}As(r)?(e=zl(r,e),t.tag=1,t=ei(null,t,r,e,n)):(t.tag=0,t=Jl(null,t,r,e,n))}return t;case 0:return Jl(e,t,t.type,t.pendingProps,n);case 1:return ei(e,t,r=t.type,o=zl(r,t.pendingProps),n);case 3:e:{if(Z(t,t.stateNode.containerInfo),null===e)throw Error(l(387));var a=t.pendingProps;r=(o=t.memoizedState).element,Ti(e,t),Mi(t,a,null,n);var i=t.memoizedState;if(a=i.cache,bi(t,Io,a),a!==o.cache&&xi(t,[Io],n,!0),Ai(),a=i.element,o.isDehydrated){if(o={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=ti(e,t,a,n);break e}if(a!==r){ao(r=Or(Error(l(424)),t)),t=ti(e,t,a,n);break e}for(Yr=id(t.stateNode.containerInfo.firstChild),Kr=t,Gr=!0,Xr=null,Jr=!0,n=So(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(oo(),a===r){t=fi(e,t,n);break e}Hl(e,t,a,n)}t=t.child}return t;case 26:return Xl(e,t),null===e?(n=gd(t.type,null,t.pendingProps,null))?t.memoizedState=n:Gr||(n=t.type,e=t.pendingProps,(r=Yu(X.current).createElement(n))[De]=t,r[Re]=e,qu(r,n,e),Xe(r),t.stateNode=r):t.memoizedState=gd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return te(t),null===e&&Gr&&(r=t.stateNode=cd(t.type,t.pendingProps,X.current),Kr=t,Jr=!0,Yr=id(r.firstChild)),r=t.pendingProps.children,null!==e||Gr?Hl(e,t,r,n):t.child=ko(t,null,r,n),Xl(e,t),t.child;case 5:return null===e&&Gr&&((o=r=Yr)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[He])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==o.rel||e.getAttribute("href")!==(null==o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===a)return e}if(null===(e=id(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,Jr))?(t.stateNode=r,Kr=t,Yr=id(r.firstChild),Jr=!1,o=!0):o=!1),o||eo(t)),te(t),o=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,r=a.children,Ju(o,a)?r=null:null!==i&&Ju(o,i)&&(t.flags|=32),null!==t.memoizedState&&(o=fa(e,t,ha,null,null,n),Ad._currentValue=o),Xl(e,t),Hl(e,t,r,n),t.child;case 6:return null===e&&Gr&&((e=n=Yr)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=id(e.nextSibling)))return null}return e}(n,t.pendingProps,Jr))?(t.stateNode=n,Kr=t,Yr=null,e=!0):e=!1),e||eo(t)),null;case 13:return ai(e,t,n);case 4:return Z(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ko(t,null,r,n):Hl(e,t,r,n),t.child;case 11:return ql(e,t,t.type,t.pendingProps,n);case 7:return Hl(e,t,t.pendingProps,n),t.child;case 8:case 12:return Hl(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,bi(t,t.type,r.value),Hl(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,Ni(t),r=r(o=Ei(o)),t.flags|=1,Hl(e,t,r,n),t.child;case 14:return Ql(e,t,t.type,t.pendingProps,n);case 15:return Kl(e,t,t.type,t.pendingProps,n);case 19:return di(e,t,n);case 22:return Yl(e,t,n);case 24:return Ni(t),r=Ei(Io),null===e?(null===(o=Go())&&(o=tc,a=Uo(),o.pooledCache=a,a.refCount++,null!==a&&(o.pooledCacheLanes|=n),o=a),t.memoizedState={parent:r,cache:o},_i(t),bi(t,Io,o)):(0!==(e.lanes&n)&&(Ti(e,t),Mi(t,null,null,n),Ai()),o=e.memoizedState,a=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=o),bi(t,Io,r)):(r=a.cache,bi(t,Io,r),r!==o.cache&&xi(t,[Io],n,!0))),Hl(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(l(156,t.tag))}var hi=q(null),gi=null,vi=null;function bi(e,t,n){K(hi,t._currentValue),t._currentValue=n}function yi(e){e._currentValue=hi.current,Q(hi)}function wi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xi(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=o.dependencies;if(null!==a){var i=o.child;a=a.firstContext;e:for(;null!==a;){var s=a;a=o;for(var c=0;c<t.length;c++)if(s.context===t[c]){a.lanes|=n,null!==(s=a.alternate)&&(s.lanes|=n),wi(a.return,n,e),r||(i=null);break e}a=s.next}}else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(a=i.alternate)&&(a.lanes|=n),wi(i,n,e),i=null}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===e){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}}function ki(e,t,n,r){e=null;for(var o=t,a=!1;null!==o;){if(!a)if(0!==(524288&o.flags))a=!0;else if(0!==(262144&o.flags))break;if(10===o.tag){var i=o.alternate;if(null===i)throw Error(l(387));if(null!==(i=i.memoizedProps)){var s=o.type;Kn(o.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(o===J.current){if(null===(i=o.alternate))throw Error(l(387));i.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(null!==e?e.push(Ad):e=[Ad])}o=o.return}null!==e&&xi(t,e,n,r),t.flags|=262144}function Si(e){for(e=e.firstContext;null!==e;){if(!Kn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ni(e){gi=e,vi=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ei(e){return Ci(gi,e)}function ji(e,t){return null===gi&&Ni(e),Ci(e,t)}function Ci(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===vi){if(null===e)throw Error(l(308));vi=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else vi=vi.next=t;return n}var Pi=!1;function _i(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ti(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function $i(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Li(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&ec)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=$r(e),Tr(e,null,n),t}return Cr(e,r,t,n),$r(e)}function zi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194176&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}function Oi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var l={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?o=a=l:a=a.next=l,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Fi=!1;function Ai(){if(Fi){if(null!==qo)throw qo}}function Mi(e,t,n,r){Fi=!1;var o=e.updateQueue;Pi=!1;var a=o.firstBaseUpdate,l=o.lastBaseUpdate,i=o.shared.pending;if(null!==i){o.shared.pending=null;var s=i,c=s.next;s.next=null,null===l?a=c:l.next=c,l=s;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(l=0,u=c=s=null,i=a;;){var f=-536870913&i.lane,p=f!==i.lane;if(p?(rc&f)===f:(r&f)===f){0!==f&&f===Ho&&(Fi=!0),null!==u&&(u=u.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var m=e,h=i;f=t;var g=n;switch(h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(g,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(g,d,f):m)||void 0===f)break e;d=$({},d,f);break e;case 2:Pi=!0}}null!==(f=i.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=o.callbacks)?o.callbacks=[f]:p.push(f))}else p={lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,l|=f;if(null===(i=i.next)){if(null===(i=o.shared.pending))break;i=(p=i).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null===a&&(o.shared.lanes=0),dc|=l,e.lanes=l,e.memoizedState=d}}function Di(e,t){if("function"!==typeof e)throw Error(l(191,e));e.call(t)}function Ri(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)Di(n[e],t)}function Ii(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var a=n.create,l=n.inst;r=a(),l.destroy=r}n=n.next}while(n!==o)}}catch(i){ru(t,t.return,i)}}function Ui(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var a=o.next;r=a;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,o=t;var s=n;try{i()}catch(c){ru(o,s,c)}}}r=r.next}while(r!==a)}}catch(c){ru(t,t.return,c)}}function Bi(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{Ri(t,n)}catch(r){ru(e,e.return,r)}}}function Vi(e,t,n){n.props=zl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){ru(e,t,r)}}function Wi(e,t){try{var n=e.ref;if(null!==n){var r=e.stateNode;switch(e.tag){case 26:case 27:case 5:var o=r;break;default:o=r}"function"===typeof n?e.refCleanup=n(o):n.current=o}}catch(a){ru(e,t,a)}}function Hi(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(o){ru(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){ru(e,t,a)}else n.current=null}function qi(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){ru(e,e.return,o)}}function Qi(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,a=null,i=null,s=null,c=null,u=null,d=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&null!=f)switch(m){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(m)||Wu(e,t,m,null,r,f)}}for(var p in r){var m=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=m||null!=f))switch(p){case"type":a=m;break;case"name":o=m;break;case"checked":u=m;break;case"defaultChecked":d=m;break;case"value":i=m;break;case"defaultValue":s=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(l(137,t));break;default:m!==f&&Wu(e,t,p,m,r,f)}}return void gt(e,i,s,c,u,d,a,o);case"select":for(a in m=i=s=p=null,n)if(c=n[a],n.hasOwnProperty(a)&&null!=c)switch(a){case"value":break;case"multiple":m=c;default:r.hasOwnProperty(a)||Wu(e,t,a,null,r,c)}for(o in r)if(a=r[o],c=n[o],r.hasOwnProperty(o)&&(null!=a||null!=c))switch(o){case"value":p=a;break;case"defaultValue":s=a;break;case"multiple":i=a;default:a!==c&&Wu(e,t,o,a,r,c)}return t=s,n=i,r=m,void(null!=p?yt(e,!!n,p,!1):!!r!==!!n&&(null!=t?yt(e,!!n,t,!0):yt(e,!!n,n?[]:"",!1)));case"textarea":for(s in m=p=null,n)if(o=n[s],n.hasOwnProperty(s)&&null!=o&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Wu(e,t,s,null,r,o)}for(i in r)if(o=r[i],a=n[i],r.hasOwnProperty(i)&&(null!=o||null!=a))switch(i){case"value":p=o;break;case"defaultValue":m=o;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(l(91));break;default:o!==a&&Wu(e,t,i,o,r,a)}return void wt(e,p,m);case"option":for(var h in n)if(p=n[h],n.hasOwnProperty(h)&&null!=p&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else Wu(e,t,h,null,r,p);for(c in r)if(p=r[c],m=n[c],r.hasOwnProperty(c)&&p!==m&&(null!=p||null!=m))if("selected"===c)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Wu(e,t,c,p,r,m);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Wu(e,t,g,null,r,p);for(u in r)if(p=r[u],m=n[u],r.hasOwnProperty(u)&&p!==m&&(null!=p||null!=m))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(l(137,t));break;default:Wu(e,t,u,p,r,m)}return;default:if(jt(t)){for(var v in n)p=n[v],n.hasOwnProperty(v)&&void 0!==p&&!r.hasOwnProperty(v)&&Hu(e,t,v,void 0,r,p);for(d in r)p=r[d],m=n[d],!r.hasOwnProperty(d)||p===m||void 0===p&&void 0===m||Hu(e,t,d,p,r,m);return}}for(var b in n)p=n[b],n.hasOwnProperty(b)&&null!=p&&!r.hasOwnProperty(b)&&Wu(e,t,b,null,r,p);for(f in r)p=r[f],m=n[f],!r.hasOwnProperty(f)||p===m||null==p&&null==m||Wu(e,t,f,p,r,m)}(r,e.type,n,t),r[Re]=t}catch(o){ru(e,e.return,o)}}function Ki(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag||4===e.tag}function Yi(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||Ki(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&27!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function Gi(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Vu));else if(4!==r&&27!==r&&null!==(e=e.child))for(Gi(e,t,n),e=e.sibling;null!==e;)Gi(e,t,n),e=e.sibling}function Xi(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&27!==r&&null!==(e=e.child))for(Xi(e,t,n),e=e.sibling;null!==e;)Xi(e,t,n),e=e.sibling}var Ji=!1,Zi=!1,es=!1,ts="function"===typeof WeakSet?WeakSet:Set,ns=null,rs=!1;function os(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:vs(e,n),4&r&&Ii(5,n);break;case 1:if(vs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){ru(n,n.return,i)}else{var o=zl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){ru(n,n.return,s)}}64&r&&Bi(n),512&r&&Wi(n,n.return);break;case 3:if(vs(e,n),64&r&&null!==(r=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:e=n.child.stateNode}try{Ri(r,e)}catch(i){ru(n,n.return,i)}}break;case 26:vs(e,n),512&r&&Wi(n,n.return);break;case 27:case 5:vs(e,n),null===t&&4&r&&qi(n),512&r&&Wi(n,n.return);break;case 12:default:vs(e,n);break;case 13:vs(e,n),4&r&&us(e,n);break;case 22:if(!(o=null!==n.memoizedState||Ji)){t=null!==t&&null!==t.memoizedState||Zi;var a=Ji,l=Zi;Ji=o,(Zi=t)&&!l?ys(e,n,0!==(8772&n.subtreeFlags)):vs(e,n),Ji=a,Zi=l}512&r&&("manual"===n.memoizedProps.mode?Wi(n,n.return):Hi(n,n.return))}}function as(e){var t=e.alternate;null!==t&&(e.alternate=null,as(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&qe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ls=null,is=!1;function ss(e,t,n){for(n=n.child;null!==n;)cs(e,t,n),n=n.sibling}function cs(e,t,n){if(be&&"function"===typeof be.onCommitFiberUnmount)try{be.onCommitFiberUnmount(ve,n)}catch(l){}switch(n.tag){case 26:Zi||Hi(n,t),ss(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:Zi||Hi(n,t);var r=ls,o=is;for(ls=n.stateNode,ss(e,t,n),t=(n=n.stateNode).attributes;t.length;)n.removeAttributeNode(t[0]);qe(n),ls=r,is=o;break;case 5:Zi||Hi(n,t);case 6:o=ls;var a=is;if(ls=null,ss(e,t,n),is=a,null!==(ls=o))if(is)try{e=ls,r=n.stateNode,8===e.nodeType?e.parentNode.removeChild(r):e.removeChild(r)}catch(i){ru(n,t,i)}else try{ls.removeChild(n.stateNode)}catch(i){ru(n,t,i)}break;case 18:null!==ls&&(is?(t=ls,n=n.stateNode,8===t.nodeType?ad(t.parentNode,n):1===t.nodeType&&ad(t,n),gf(t)):ad(ls,n.stateNode));break;case 4:r=ls,o=is,ls=n.stateNode.containerInfo,is=!0,ss(e,t,n),ls=r,is=o;break;case 0:case 11:case 14:case 15:Zi||Ui(2,n,t),Zi||Ui(4,n,t),ss(e,t,n);break;case 1:Zi||(Hi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&Vi(n,t,r)),ss(e,t,n);break;case 21:ss(e,t,n);break;case 22:Zi||Hi(n,t),Zi=(r=Zi)||null!==n.memoizedState,ss(e,t,n),Zi=r;break;default:ss(e,t,n)}}function us(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{gf(e)}catch(n){ru(t,t.return,n)}}function ds(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ts),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ts),t;default:throw Error(l(435,e.tag))}}(e);t.forEach((function(t){var r=su.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function fs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r],a=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:case 5:ls=s.stateNode,is=!1;break e;case 3:case 4:ls=s.stateNode.containerInfo,is=!0;break e}s=s.return}if(null===ls)throw Error(l(160));cs(a,i,o),ls=null,is=!1,null!==(a=o.alternate)&&(a.return=null),o.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)ms(t,e),t=t.sibling}var ps=null;function ms(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:fs(t,e),hs(e),4&r&&(Ui(3,e,e.return),Ii(3,e),Ui(5,e,e.return));break;case 1:fs(t,e),hs(e),512&r&&(Zi||null===n||Hi(n,n.return)),64&r&&Ji&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var o=ps;if(fs(t,e),hs(e),512&r&&(Zi||null===n||Hi(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":(!(a=o.getElementsByTagName("title")[0])||a[He]||a[De]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=o.createElement(r),o.head.insertBefore(a,o.querySelector("head > title"))),qu(a,r,n),a[De]=e,Xe(a),r=a;break e;case"link":var i=Cd("link","href",o).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((a=i[s]).getAttribute("href")===(null==n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}qu(a=o.createElement(r),r,n),o.head.appendChild(a);break;case"meta":if(i=Cd("meta","content",o).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((a=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}qu(a=o.createElement(r),r,n),o.head.appendChild(a);break;default:throw Error(l(468,r))}a[De]=e,Xe(a),r=a}e.stateNode=r}else Pd(o,e.type,e.stateNode);else e.stateNode=kd(o,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?Pd(o,e.type,e.stateNode):kd(o,r,e.memoizedProps)):null===r&&null!==e.stateNode&&Qi(e,e.memoizedProps,n.memoizedProps)}break;case 27:if(4&r&&null===e.alternate){o=e.stateNode,a=e.memoizedProps;try{for(var c=o.firstChild;c;){var u=c.nextSibling,d=c.nodeName;c[He]||"HEAD"===d||"BODY"===d||"SCRIPT"===d||"STYLE"===d||"LINK"===d&&"stylesheet"===c.rel.toLowerCase()||o.removeChild(c),c=u}for(var f=e.type,p=o.attributes;p.length;)o.removeAttributeNode(p[0]);qu(o,f,a),o[De]=e,o[Re]=a}catch(h){ru(e,e.return,h)}}case 5:if(fs(t,e),hs(e),512&r&&(Zi||null===n||Hi(n,n.return)),32&e.flags){o=e.stateNode;try{kt(o,"")}catch(h){ru(e,e.return,h)}}4&r&&null!=e.stateNode&&Qi(e,o=e.memoizedProps,null!==n?n.memoizedProps:o),1024&r&&(es=!0);break;case 6:if(fs(t,e),hs(e),4&r){if(null===e.stateNode)throw Error(l(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){ru(e,e.return,h)}}break;case 3:if(jd=null,o=ps,ps=fd(t.containerInfo),fs(t,e),ps=o,hs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{gf(t.containerInfo)}catch(h){ru(e,e.return,h)}es&&(es=!1,gs(e));break;case 4:r=ps,ps=fd(e.stateNode.containerInfo),fs(t,e),hs(e),ps=r;break;case 12:fs(t,e),hs(e);break;case 13:fs(t,e),hs(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(yc=se()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,ds(e,r)));break;case 22:if(512&r&&(Zi||null===n||Hi(n,n.return)),c=null!==e.memoizedState,u=null!==n&&null!==n.memoizedState,Ji=(d=Ji)||c,Zi=(f=Zi)||u,fs(t,e),Zi=f,Ji=d,hs(e),(t=e.stateNode)._current=e,t._visibility&=-3,t._visibility|=2&t._pendingVisibility,8192&r&&(t._visibility=c?-2&t._visibility:1|t._visibility,c&&(t=Ji||Zi,null===n||u||t||bs(e)),null===e.memoizedProps||"manual"!==e.memoizedProps.mode))e:for(n=null,t=e;;){if(5===t.tag||26===t.tag||27===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,c)"function"===typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none";else{i=u.stateNode;var m=void 0!==(s=u.memoizedProps.style)&&null!==s&&s.hasOwnProperty("display")?s.display:null;i.style.display=null==m||"boolean"===typeof m?"":(""+m).trim()}}catch(h){ru(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=c?"":u.memoizedProps}catch(h){ru(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,ds(e,n))));break;case 19:fs(t,e),hs(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,ds(e,r)));break;case 21:break;default:fs(t,e),hs(e)}}function hs(e){var t=e.flags;if(2&t){try{if(27!==e.tag){e:{for(var n=e.return;null!==n;){if(Ki(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 27:var o=r.stateNode;Xi(e,Yi(e),o);break;case 5:var a=r.stateNode;32&r.flags&&(kt(a,""),r.flags&=-33),Xi(e,Yi(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;Gi(e,Yi(e),i);break;default:throw Error(l(161))}}}catch(s){ru(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function gs(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;gs(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function vs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)os(e,t.alternate,t),t=t.sibling}function bs(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ui(4,t,t.return),bs(t);break;case 1:Hi(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&Vi(t,t.return,n),bs(t);break;case 26:case 27:case 5:Hi(t,t.return),bs(t);break;case 22:Hi(t,t.return),null===t.memoizedState&&bs(t);break;default:bs(t)}e=e.sibling}}function ys(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,a=t,l=a.flags;switch(a.tag){case 0:case 11:case 15:ys(o,a,n),Ii(4,a);break;case 1:if(ys(o,a,n),"function"===typeof(o=(r=a).stateNode).componentDidMount)try{o.componentDidMount()}catch(c){ru(r,r.return,c)}if(null!==(o=(r=a).updateQueue)){var i=r.stateNode;try{var s=o.shared.hiddenCallbacks;if(null!==s)for(o.shared.hiddenCallbacks=null,o=0;o<s.length;o++)Di(s[o],i)}catch(c){ru(r,r.return,c)}}n&&64&l&&Bi(a),Wi(a,a.return);break;case 26:case 27:case 5:ys(o,a,n),n&&null===r&&4&l&&qi(a),Wi(a,a.return);break;case 12:default:ys(o,a,n);break;case 13:ys(o,a,n),n&&4&l&&us(o,a);break;case 22:null===a.memoizedState&&ys(o,a,n),Wi(a,a.return)}t=t.sibling}}function ws(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Bo(n))}function xs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Bo(e))}function ks(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ss(e,t,n,r),t=t.sibling}function Ss(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:ks(e,t,n,r),2048&o&&Ii(9,t);break;case 3:ks(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Bo(e)));break;case 12:if(2048&o){ks(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,l=a.id,i=a.onPostCommit;"function"===typeof i&&i(l,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){ru(t,t.return,s)}}else ks(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,null!==t.memoizedState?4&a._visibility?ks(e,t,n,r):Es(e,t):4&a._visibility?ks(e,t,n,r):(a._visibility|=4,Ns(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&o&&ws(t.alternate,t);break;case 24:ks(e,t,n,r),2048&o&&xs(t.alternate,t);break;default:ks(e,t,n,r)}}function Ns(e,t,n,r,o){for(o=o&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,l=t,i=n,s=r,c=l.flags;switch(l.tag){case 0:case 11:case 15:Ns(a,l,i,s,o),Ii(8,l);break;case 23:break;case 22:var u=l.stateNode;null!==l.memoizedState?4&u._visibility?Ns(a,l,i,s,o):Es(a,l):(u._visibility|=4,Ns(a,l,i,s,o)),o&&2048&c&&ws(l.alternate,l);break;case 24:Ns(a,l,i,s,o),o&&2048&c&&xs(l.alternate,l);break;default:Ns(a,l,i,s,o)}t=t.sibling}}function Es(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Es(n,r),2048&o&&ws(r.alternate,r);break;case 24:Es(n,r),2048&o&&xs(r.alternate,r);break;default:Es(n,r)}t=t.sibling}}var js=8192;function Cs(e){if(e.subtreeFlags&js)for(e=e.child;null!==e;)Ps(e),e=e.sibling}function Ps(e){switch(e.tag){case 26:Cs(e),e.flags&js&&null!==e.memoizedState&&function(e,t,n){if(null===Td)throw Error(l(475));var r=Td;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var o=vd(n.href),a=e.querySelector(bd(o));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Ld.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void Xe(a);a=e.ownerDocument||e,n=yd(n),(o=ud.get(o))&&Nd(n,o),Xe(a=a.createElement("link"));var i=a;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),qu(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Ld.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(ps,e.memoizedState,e.memoizedProps);break;case 5:default:Cs(e);break;case 3:case 4:var t=ps;ps=fd(e.stateNode.containerInfo),Cs(e),ps=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=js,js=16777216,Cs(e),js=t):Cs(e))}}function _s(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ts(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ns=r,zs(r,e)}_s(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)$s(e),e=e.sibling}function $s(e){switch(e.tag){case 0:case 11:case 15:Ts(e),2048&e.flags&&Ui(9,e,e.return);break;case 3:case 12:default:Ts(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&4&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-5,Ls(e)):Ts(e)}}function Ls(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ns=r,zs(r,e)}_s(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:Ui(8,t,t.return),Ls(t);break;case 22:4&(n=t.stateNode)._visibility&&(n._visibility&=-5,Ls(t));break;default:Ls(t)}e=e.sibling}}function zs(e,t){for(;null!==ns;){var n=ns;switch(n.tag){case 0:case 11:case 15:Ui(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Bo(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,ns=r;else e:for(n=e;null!==ns;){var o=(r=ns).sibling,a=r.return;if(as(r),r===n){ns=null;break e}if(null!==o){o.return=a,ns=o;break e}ns=a}}}function Os(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Fs(e,t,n,r){return new Os(e,t,n,r)}function As(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ms(e,t){var n=e.alternate;return null===n?((n=Fs(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=31457280&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ds(e,t){e.flags&=31457282;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Rs(e,t,n,r,o,a){var i=0;if(r=e,"function"===typeof e)As(e)&&(i=1);else if("string"===typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,Y.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case d:return Is(n.children,o,a,t);case f:i=8,o|=24;break;case p:return(e=Fs(12,n,t,2|o)).elementType=p,e.lanes=a,e;case b:return(e=Fs(13,n,t,o)).elementType=b,e.lanes=a,e;case y:return(e=Fs(19,n,t,o)).elementType=y,e.lanes=a,e;case k:return Us(n,o,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case m:case g:i=10;break e;case h:i=9;break e;case v:i=11;break e;case w:i=14;break e;case x:i=16,r=null;break e}i=29,n=Error(l(130,null===e?"null":typeof e,"")),r=null}return(t=Fs(i,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Is(e,t,n,r){return(e=Fs(7,e,r,t)).lanes=n,e}function Us(e,t,n,r){(e=Fs(22,e,r,t)).elementType=k,e.lanes=n;var o={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=o._current;if(null===e)throw Error(l(456));if(0===(2&o._pendingVisibility)){var t=_r(e,2);null!==t&&(o._pendingVisibility|=2,Lc(t,e,2))}},attach:function(){var e=o._current;if(null===e)throw Error(l(456));if(0!==(2&o._pendingVisibility)){var t=_r(e,2);null!==t&&(o._pendingVisibility&=-3,Lc(t,e,2))}}};return e.stateNode=o,e}function Bs(e,t,n){return(e=Fs(6,e,null,t)).lanes=n,e}function Vs(e,t,n){return(t=Fs(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ws(e){e.flags|=4}function Hs(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!_d(t)){if(null!==(t=_o.current)&&((4194176&rc)===rc?null!==To:(62914560&rc)!==rc&&0===(536870912&rc)||t!==To))throw po=so,io;e.flags|=8192}}function qs(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Te():536870912,e.lanes|=t,hc|=t)}function Qs(e,t){if(!Gr)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ks(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=31457280&o.subtreeFlags,r|=31457280&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ys(e,t,n){var r=t.pendingProps;switch(Qr(t),t.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return Ks(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),yi(Io),ee(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(ro(t)?Ws(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==Xr&&(Oc(Xr),Xr=null))),Ks(t),null;case 26:return n=t.memoizedState,null===e?(Ws(t),null!==n?(Ks(t),Hs(t,n)):(Ks(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ws(t),Ks(t),Hs(t,n)):(Ks(t),t.flags&=-16777217):(e.memoizedProps!==r&&Ws(t),Ks(t),t.flags&=-16777217),null;case 27:ne(t),n=X.current;var o=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ws(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Ks(t),null}e=Y.current,ro(t)?to(t):(e=cd(o,r,n),t.stateNode=e,Ws(t))}return Ks(t),null;case 5:if(ne(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ws(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Ks(t),null}if(e=Y.current,ro(t))to(t);else{switch(o=Yu(X.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?o.createElement(n,{is:r.is}):o.createElement(n)}}e[De]=t,e[Re]=r;e:for(o=t.child;null!==o;){if(5===o.tag||6===o.tag)e.appendChild(o.stateNode);else if(4!==o.tag&&27!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;null===o.sibling;){if(null===o.return||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(qu(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ws(t)}}return Ks(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Ws(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(e=X.current,ro(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(o=Kr))switch(o.tag){case 27:case 5:r=o.memoizedProps}e[De]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Bu(e.nodeValue,n)))||eo(t)}else(e=Yu(e).createTextNode(r))[De]=t,t.stateNode=e}return Ks(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(o=ro(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[De]=t}else oo(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ks(t),o=!1}else null!==Xr&&(Oc(Xr),Xr=null),o=!0;if(!o)return 256&t.flags?(Oo(t),t):(Oo(t),null)}if(Oo(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){o=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(o=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),qs(t,t.updateQueue),Ks(t),null;case 4:return ee(),null===e&&Lu(t.stateNode.containerInfo),Ks(t),null;case 10:return yi(t.type),Ks(t),null;case 19:if(Q(Fo),null===(o=t.memoizedState))return Ks(t),null;if(r=0!==(128&t.flags),null===(a=o.rendering))if(r)Qs(o,!1);else{if(0!==uc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=Ao(e))){for(t.flags|=128,Qs(o,!1),e=a.updateQueue,t.updateQueue=e,qs(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ds(n,e),n=n.sibling;return K(Fo,1&Fo.current|2),t.child}e=e.sibling}null!==o.tail&&se()>wc&&(t.flags|=128,r=!0,Qs(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=Ao(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,qs(t,e),Qs(o,!0),null===o.tail&&"hidden"===o.tailMode&&!a.alternate&&!Gr)return Ks(t),null}else 2*se()-o.renderingStartTime>wc&&536870912!==n&&(t.flags|=128,r=!0,Qs(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=o.last)?e.sibling=a:t.child=a,o.last=a)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=se(),t.sibling=null,e=Fo.current,K(Fo,r?1&e|2:1&e),t):(Ks(t),null);case 22:case 23:return Oo(t),Po(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(Ks(t),6&t.subtreeFlags&&(t.flags|=8192)):Ks(t),null!==(n=t.updateQueue)&&qs(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&Q(Yo),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),yi(Io),Ks(t),null;case 25:return null}throw Error(l(156,t.tag))}function Gs(e,t){switch(Qr(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return yi(Io),ee(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return ne(t),null;case 13:if(Oo(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));oo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Q(Fo),null;case 4:return ee(),null;case 10:return yi(t.type),null;case 22:case 23:return Oo(t),Po(),null!==e&&Q(Yo),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return yi(Io),null;default:return null}}function Xs(e,t){switch(Qr(t),t.tag){case 3:yi(Io),ee();break;case 26:case 27:case 5:ne(t);break;case 4:ee();break;case 13:Oo(t);break;case 19:Q(Fo);break;case 10:yi(t.type);break;case 22:case 23:Oo(t),Po(),null!==e&&Q(Yo);break;case 24:yi(Io)}}var Js={getCacheForType:function(e){var t=Ei(Io),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},Zs="function"===typeof WeakMap?WeakMap:Map,ec=0,tc=null,nc=null,rc=0,oc=0,ac=null,lc=!1,ic=!1,sc=!1,cc=0,uc=0,dc=0,fc=0,pc=0,mc=0,hc=0,gc=null,vc=null,bc=!1,yc=0,wc=1/0,xc=null,kc=null,Sc=!1,Nc=null,Ec=0,jc=0,Cc=null,Pc=0,_c=null;function Tc(){if(0!==(2&ec)&&0!==rc)return rc&-rc;if(null!==T.T){return 0!==Ho?Ho:xu()}return Ae()}function $c(){0===mc&&(mc=0===(536870912&rc)||Gr?_e():536870912);var e=_o.current;return null!==e&&(e.flags|=32),mc}function Lc(e,t,n){(e===tc&&2===oc||null!==e.cancelPendingCommit)&&(Ic(e,0),Mc(e,rc,mc,!1)),Le(e,n),0!==(2&ec)&&e===tc||(e===tc&&(0===(2&ec)&&(fc|=n),4===uc&&Mc(e,rc,mc,!1)),hu(e))}function zc(e,t,n){if(0!==(6&ec))throw Error(l(327));for(var r=!n&&0===(60&t)&&0===(t&e.expiredLanes)||Ce(e,t),o=r?function(e,t){var n=ec;ec|=2;var r=Bc(),o=Vc();tc!==e||rc!==t?(xc=null,wc=se()+500,Ic(e,t)):ic=Ce(e,t);e:for(;;)try{if(0!==oc&&null!==nc){t=nc;var a=ac;t:switch(oc){case 1:oc=0,ac=null,Gc(e,t,a,1);break;case 2:if(co(a)){oc=0,ac=null,Yc(t);break}t=function(){2===oc&&tc===e&&(oc=7),hu(e)},a.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:co(a)?(oc=0,ac=null,Yc(t)):(oc=0,ac=null,Gc(e,t,a,7));break;case 5:var i=null;switch(nc.tag){case 26:i=nc.memoizedState;case 5:case 27:var s=nc;if(!i||_d(i)){oc=0,ac=null;var c=s.sibling;if(null!==c)nc=c;else{var u=s.return;null!==u?(nc=u,Xc(u)):nc=null}break t}}oc=0,ac=null,Gc(e,t,a,5);break;case 6:oc=0,ac=null,Gc(e,t,a,6);break;case 8:Rc(),uc=6;break e;default:throw Error(l(462))}}Qc();break}catch(d){Uc(e,d)}return vi=gi=null,T.H=r,T.A=o,ec=n,null!==nc?0:(tc=null,rc=0,jr(),uc)}(e,t):Hc(e,t,!0),a=r;;){if(0===o){ic&&!r&&Mc(e,t,0,!1);break}if(6===o)Mc(e,t,0,!lc);else{if(n=e.current.alternate,a&&!Ac(n)){o=Hc(e,t,!1),a=!1;continue}if(2===o){if(a=t,e.errorRecoveryDisabledLanes&a)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;o=gc;var c=s.current.memoizedState.isDehydrated;if(c&&(Ic(s,i).flags|=256),2!==(i=Hc(s,i,!1))){if(sc&&!c){s.errorRecoveryDisabledLanes|=a,fc|=a,o=4;break e}a=vc,vc=o,null!==a&&Oc(a)}o=i}if(a=!1,2!==o)continue}}if(1===o){Ic(e,0),Mc(e,t,0,!0);break}e:{switch(r=e,o){case 0:case 1:throw Error(l(345));case 4:if((4194176&t)===t){Mc(r,t,mc,!lc);break e}break;case 2:vc=null;break;case 3:case 5:break;default:throw Error(l(329))}if(r.finishedWork=n,r.finishedLanes=t,(62914560&t)===t&&10<(a=yc+300-se())){if(Mc(r,t,mc,!lc),0!==je(r,0))break e;r.timeoutHandle=ed(Fc.bind(null,r,n,vc,xc,bc,t,mc,fc,hc,lc,2,-0,0),a)}else Fc(r,n,vc,xc,bc,t,mc,fc,hc,lc,0,-0,0)}}break}hu(e)}function Oc(e){null===vc?vc=e:vc.push.apply(vc,e)}function Fc(e,t,n,r,o,a,i,s,c,u,d,f,p){var m=t.subtreeFlags;if((8192&m||16785408===(16785408&m))&&(Td={stylesheets:null,count:0,unsuspend:$d},Ps(t),null!==(t=function(){if(null===Td)throw Error(l(475));var e=Td;return e.stylesheets&&0===e.count&&Od(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Od(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=t(Zc.bind(null,e,n,r,o,i,s,c,1,f,p)),void Mc(e,a,i,!u);Zc(e,n,r,o,i,s,c,d,f,p)}function Ac(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!Kn(a(),o))return!1}catch(l){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Mc(e,t,n,r){t&=~pc,t&=~fc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var a=31-we(o),l=1<<a;r[a]=-1,o&=~l}0!==n&&ze(e,n,t)}function Dc(){return 0!==(6&ec)||(gu(0,!1),!1)}function Rc(){if(null!==nc){if(0===oc)var e=nc.return;else vi=gi=null,ba(e=nc),ho=null,go=0,e=nc;for(;null!==e;)Xs(e.alternate,e),e=e.return;nc=null}}function Ic(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,td(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Rc(),tc=e,nc=n=Ms(e.current,null),rc=t,oc=0,ac=null,lc=!1,ic=Ce(e,t),sc=!1,hc=mc=pc=fc=dc=uc=0,vc=gc=null,bc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-we(r),a=1<<o;t|=e[o],r&=~a}return cc=t,jr(),n}function Uc(e,t){ea=null,T.H=El,t===lo?(t=mo(),oc=3):t===io?(t=mo(),oc=4):oc=t===Vl?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,ac=t,null===nc&&(uc=1,Dl(e,Or(t,e.current)))}function Bc(){var e=T.H;return T.H=El,null===e?El:e}function Vc(){var e=T.A;return T.A=Js,e}function Wc(){uc=4,lc||(4194176&rc)!==rc&&null!==_o.current||(ic=!0),0===(134217727&dc)&&0===(134217727&fc)||null===tc||Mc(tc,rc,mc,!1)}function Hc(e,t,n){var r=ec;ec|=2;var o=Bc(),a=Vc();tc===e&&rc===t||(xc=null,Ic(e,t)),t=!1;var l=uc;e:for(;;)try{if(0!==oc&&null!==nc){var i=nc,s=ac;switch(oc){case 8:Rc(),l=6;break e;case 3:case 2:case 6:null===_o.current&&(t=!0);var c=oc;if(oc=0,ac=null,Gc(e,i,s,c),n&&ic){l=0;break e}break;default:c=oc,oc=0,ac=null,Gc(e,i,s,c)}}qc(),l=uc;break}catch(u){Uc(e,u)}return t&&e.shellSuspendCounter++,vi=gi=null,ec=r,T.H=o,T.A=a,null===nc&&(tc=null,rc=0,jr()),l}function qc(){for(;null!==nc;)Kc(nc)}function Qc(){for(;null!==nc&&!le();)Kc(nc)}function Kc(e){var t=mi(e.alternate,e,cc);e.memoizedProps=e.pendingProps,null===t?Xc(e):nc=t}function Yc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Zl(n,t,t.pendingProps,t.type,void 0,rc);break;case 11:t=Zl(n,t,t.pendingProps,t.type.render,t.ref,rc);break;case 5:ba(t);default:Xs(n,t),t=mi(n,t=nc=Ds(t,cc),cc)}e.memoizedProps=e.pendingProps,null===t?Xc(e):nc=t}function Gc(e,t,n,r){vi=gi=null,ba(t),ho=null,go=0;var o=t.return;try{if(function(e,t,n,r,o){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&ki(t,n,o,!0),null!==(n=_o.current)){switch(n.tag){case 13:return null===To?Wc():null===n.alternate&&0===uc&&(uc=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===so?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),ou(e,r,o)),!1;case 22:return n.flags|=65536,r===so?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),ou(e,r,o)),!1}throw Error(l(435,n.tag))}return ou(e,r,o),Wc(),!1}if(Gr)return null!==(t=_o.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==Zr&&ao(Or(e=Error(l(422),{cause:r}),n))):(r!==Zr&&ao(Or(t=Error(l(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,o&=-o,e.lanes|=o,r=Or(r,n),Oi(e,o=Il(e.stateNode,r,o)),4!==uc&&(uc=2)),!1;var a=Error(l(520),{cause:r});if(a=Or(a,n),null===gc?gc=[a]:gc.push(a),4!==uc&&(uc=2),null===t)return!0;r=Or(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,Oi(n,e=Il(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===kc||!kc.has(a))))return n.flags|=65536,o&=-o,n.lanes|=o,Bl(o=Ul(o),e,n,r),Oi(n,o),!1}n=n.return}while(null!==n);return!1}(e,o,t,n,rc))return uc=1,Dl(e,Or(n,e.current)),void(nc=null)}catch(a){if(null!==o)throw nc=o,a;return uc=1,Dl(e,Or(n,e.current)),void(nc=null)}32768&t.flags?(Gr||1===r?e=!0:ic||0!==(536870912&rc)?e=!1:(lc=e=!0,(2===r||3===r||6===r)&&(null!==(r=_o.current)&&13===r.tag&&(r.flags|=16384))),Jc(t,e)):Xc(t)}function Xc(e){var t=e;do{if(0!==(32768&t.flags))return void Jc(t,lc);e=t.return;var n=Ys(t.alternate,t,cc);if(null!==n)return void(nc=n);if(null!==(t=t.sibling))return void(nc=t);nc=t=e}while(null!==t);0===uc&&(uc=5)}function Jc(e,t){do{var n=Gs(e.alternate,e);if(null!==n)return n.flags&=32767,void(nc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(nc=e);nc=e=n}while(null!==e);uc=6,nc=null}function Zc(e,t,n,r,o,a,i,s,c,u){var d=T.T,f=B.p;try{B.p=2,T.T=null,function(e,t,n,r,o,a,i,s){do{tu()}while(null!==Nc);if(0!==(6&ec))throw Error(l(327));var c=e.finishedWork;if(r=e.finishedLanes,null===c)return null;if(e.finishedWork=null,e.finishedLanes=0,c===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var u=c.lanes|c.childLanes;if(function(e,t,n,r,o,a){var l=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,c=e.hiddenUpdates;for(n=l&~n;0<n;){var u=31-we(n),d=1<<u;i[u]=0,s[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var p=f[u];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&ze(e,r,0),0!==a&&0===o&&0!==e.tag&&(e.suspendedLanes|=a&~(l&~t))}(e,r,u|=Er,a,i,s),e===tc&&(nc=tc=null,rc=0),0===(10256&c.subtreeFlags)&&0===(10256&c.flags)||Sc||(Sc=!0,jc=u,Cc=n,function(e,t){oe(e,t)}(fe,(function(){return tu(),null}))),n=0!==(15990&c.flags),0!==(15990&c.subtreeFlags)||n?(n=T.T,T.T=null,a=B.p,B.p=2,i=ec,ec|=4,function(e,t){if(e=e.containerInfo,Qu=Wd,er(e=Zn(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var i=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==o&&3!==f.nodeType||(s=i+o),f!==a||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++u===o&&(s=i),p===a&&++d===r&&(c=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ku={focusedElem:e,selectionRange:n},Wd=!1,ns=t;null!==ns;)if(e=(t=ns).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,ns=e;else for(;null!==ns;){switch(a=(t=ns).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,o=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var h=zl(n.type,o,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){ru(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))ld(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ld(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(l(163))}if(null!==(e=t.sibling)){e.return=t.return,ns=e;break}ns=t.return}h=rs,rs=!1}(e,c),ms(c,e),tr(Ku,e.containerInfo),Wd=!!Qu,Ku=Qu=null,e.current=c,os(e,c.alternate,c),ie(),ec=i,B.p=a,T.T=n):e.current=c,Sc?(Sc=!1,Nc=e,Ec=r):eu(e,u),u=e.pendingLanes,0===u&&(kc=null),function(e){if(be&&"function"===typeof be.onCommitFiberRoot)try{be.onCommitFiberRoot(ve,e,void 0,128===(128&e.current.flags))}catch(t){}}(c.stateNode),hu(e),null!==t)for(o=e.onRecoverableError,c=0;c<t.length;c++)u=t[c],o(u.value,{componentStack:u.stack});0!==(3&Ec)&&tu(),u=e.pendingLanes,0!==(4194218&r)&&0!==(42&u)?e===_c?Pc++:(Pc=0,_c=e):Pc=0,gu(0,!1)}(e,t,n,r,f,o,a,i)}finally{T.T=d,B.p=f}}function eu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Bo(t)))}function tu(){if(null!==Nc){var e=Nc,t=jc;jc=0;var n=Fe(Ec),r=T.T,o=B.p;try{if(B.p=32>n?32:n,T.T=null,null===Nc)var a=!1;else{n=Cc,Cc=null;var i=Nc,s=Ec;if(Nc=null,Ec=0,0!==(6&ec))throw Error(l(331));var c=ec;if(ec|=4,$s(i.current),Ss(i,i.current,s,n),ec=c,gu(0,!1),be&&"function"===typeof be.onPostCommitFiberRoot)try{be.onPostCommitFiberRoot(ve,i)}catch(u){}a=!0}return a}finally{B.p=o,T.T=r,eu(e,t)}}return!1}function nu(e,t,n){t=Or(n,t),null!==(e=Li(e,t=Il(e.stateNode,t,2),2))&&(Le(e,2),hu(e))}function ru(e,t,n){if(3===e.tag)nu(e,e,n);else for(;null!==t;){if(3===t.tag){nu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===kc||!kc.has(r))){e=Or(n,e),null!==(r=Li(t,n=Ul(2),2))&&(Bl(n,r,t,e),Le(r,2),hu(r));break}}t=t.return}}function ou(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new Zs;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(sc=!0,o.add(n),e=au.bind(null,e,t,n),t.then(e,e))}function au(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,tc===e&&(rc&n)===n&&(4===uc||3===uc&&(62914560&rc)===rc&&300>se()-yc?0===(2&ec)&&Ic(e,0):pc|=n,hc===rc&&(hc=0)),hu(e)}function lu(e,t){0===t&&(t=Te()),null!==(e=_r(e,t))&&(Le(e,t),hu(e))}function iu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),lu(e,n)}function su(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(l(314))}null!==r&&r.delete(t),lu(e,n)}var cu=null,uu=null,du=!1,fu=!1,pu=!1,mu=0;function hu(e){var t;e!==uu&&null===e.next&&(null===uu?cu=uu=e:uu=uu.next=e),fu=!0,du||(du=!0,t=vu,rd((function(){0!==(6&ec)?oe(ue,t):t()})))}function gu(e,t){if(!pu&&fu){pu=!0;do{for(var n=!1,r=cu;null!==r;){if(!t)if(0!==e){var o=r.pendingLanes;if(0===o)var a=0;else{var l=r.suspendedLanes,i=r.pingedLanes;a=(1<<31-we(42|e)+1)-1,a=201326677&(a&=o&~(l&~i))?201326677&a|1:a?2|a:0}0!==a&&(n=!0,wu(r,a))}else a=rc,0===(3&(a=je(r,r===tc?a:0)))||Ce(r,a)||(n=!0,wu(r,a));r=r.next}}while(n);pu=!1}}function vu(){fu=du=!1;var e=0;0!==mu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==Zu&&(Zu=e,!0);return Zu=null,!1}()&&(e=mu),mu=0);for(var t=se(),n=null,r=cu;null!==r;){var o=r.next,a=bu(r,t);0===a?(r.next=null,null===n?cu=o:n.next=o,null===o&&(uu=n)):(n=r,(0!==e||0!==(3&a))&&(fu=!0)),r=o}gu(e,!1)}function bu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var l=31-we(a),i=1<<l,s=o[l];-1===s?0!==(i&n)&&0===(i&r)||(o[l]=Pe(i,t)):s<=t&&(e.expiredLanes|=i),a&=~i}if(n=rc,n=je(e,e===(t=tc)?n:0),r=e.callbackNode,0===n||e===t&&2===oc||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ae(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||Ce(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ae(r),Fe(n)){case 2:case 8:n=de;break;case 32:default:n=fe;break;case 268435456:n=me}return r=yu.bind(null,e),n=oe(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ae(r),e.callbackPriority=2,e.callbackNode=null,2}function yu(e,t){var n=e.callbackNode;if(tu()&&e.callbackNode!==n)return null;var r=rc;return 0===(r=je(e,e===tc?r:0))?null:(zc(e,r,t),bu(e,se()),null!=e.callbackNode&&e.callbackNode===n?yu.bind(null,e):null)}function wu(e,t){if(tu())return null;zc(e,t,!0)}function xu(){return 0===mu&&(mu=_e()),mu}function ku(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:_t(""+e)}function Su(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Nu=0;Nu<xr.length;Nu++){var Eu=xr[Nu];kr(Eu.toLowerCase(),"on"+(Eu[0].toUpperCase()+Eu.slice(1)))}kr(pr,"onAnimationEnd"),kr(mr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(gr,"onTransitionRun"),kr(vr,"onTransitionStart"),kr(br,"onTransitionCancel"),kr(yr,"onTransitionEnd"),tt("onMouseEnter",["mouseout","mouseover"]),tt("onMouseLeave",["mouseout","mouseover"]),tt("onPointerEnter",["pointerout","pointerover"]),tt("onPointerLeave",["pointerout","pointerover"]),et("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),et("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),et("onBeforeInput",["compositionend","keypress","textInput","paste"]),et("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),et("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),et("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ju="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cu=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ju));function Pu(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,c=i.currentTarget;if(i=i.listener,s!==a&&o.isPropagationStopped())break e;a=i,o.currentTarget=c;try{a(o)}catch(u){Ol(u)}o.currentTarget=null,a=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,c=i.currentTarget,i=i.listener,s!==a&&o.isPropagationStopped())break e;a=i,o.currentTarget=c;try{a(o)}catch(u){Ol(u)}o.currentTarget=null,a=s}}}}function _u(e,t){var n=t[Ue];void 0===n&&(n=t[Ue]=new Set);var r=e+"__bubble";n.has(r)||(zu(t,e,2,!1),n.add(r))}function Tu(e,t,n){var r=0;t&&(r|=4),zu(n,e,r,t)}var $u="_reactListening"+Math.random().toString(36).slice(2);function Lu(e){if(!e[$u]){e[$u]=!0,Je.forEach((function(t){"selectionchange"!==t&&(Cu.has(t)||Tu(t,!1,e),Tu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$u]||(t[$u]=!0,Tu("selectionchange",!1,t))}}function zu(e,t,n,r){switch(Xd(t)){case 2:var o=Hd;break;case 8:o=qd;break;default:o=Qd}n=o.bind(null,t,n,e),o=void 0,!Dt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ou(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===o||8===i.nodeType&&i.parentNode===o)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;l=l.return}for(;null!==i;){if(null===(l=Qe(i)))return;if(5===(s=l.tag)||6===s||26===s||27===s){r=a=l;continue e}i=i.parentNode}}r=r.return}At((function(){var r=a,o=$t(n),l=[];e:{var i=wr.get(e);if(void 0!==i){var s=Jt,c=e;switch(e){case"keypress":if(0===Wt(n))break e;case"keydown":case"keyup":s=mn;break;case"focusin":c="focus",s=on;break;case"focusout":c="blur",s=on;break;case"beforeblur":case"afterblur":s=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=rn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=gn;break;case pr:case mr:case hr:s=an;break;case yr:s=vn;break;case"scroll":case"scrollend":s=en;break;case"wheel":s=bn;break;case"copy":case"cut":case"paste":s=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=hn;break;case"toggle":case"beforetoggle":s=yn}var u=0!==(4&t),d=!u&&("scroll"===e||"scrollend"===e),f=u?null!==i?i+"Capture":null:i;u=[];for(var p,m=r;null!==m;){var h=m;if(p=h.stateNode,5!==(h=h.tag)&&26!==h&&27!==h||null===p||null===f||null!=(h=Mt(m,f))&&u.push(Fu(m,h,p)),d)break;m=m.return}0<u.length&&(i=new s(i,c,null,n,o),l.push({event:i,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===Tt||!(c=n.relatedTarget||n.fromElement)||!Qe(c)&&!c[Ie])&&(s||i)&&(i=o.window===o?o:(i=o.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?Qe(c):null)&&(d=M(c),u=c.tag,c!==d||5!==u&&27!==u&&6!==u)&&(c=null)):(s=null,c=r),s!==c)){if(u=nn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=hn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?i:Ye(s),p=null==c?i:Ye(c),(i=new u(h,m+"leave",s,n,o)).target=d,i.relatedTarget=p,h=null,Qe(o)===r&&((u=new u(f,m+"enter",c,n,o)).target=p,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(f=c,m=0,p=u=s;p;p=Mu(p))m++;for(p=0,h=f;h;h=Mu(h))p++;for(;0<m-p;)u=Mu(u),m--;for(;0<p-m;)f=Mu(f),p--;for(;m--;){if(u===f||null!==f&&u===f.alternate)break e;u=Mu(u),f=Mu(f)}u=null}else u=null;null!==s&&Du(l,i,s,u,!1),null!==c&&null!==d&&Du(l,d,c,u,!0)}if("select"===(s=(i=r?Ye(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Mn;else if($n(i))if(Dn)g=Qn;else{g=Hn;var v=Wn}else!(s=i.nodeName)||"input"!==s.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&jt(r.elementType)&&(g=Mn):g=qn;switch(g&&(g=g(e,r))?Ln(l,g,n,o):(v&&v(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&bt(i,"number",i.value)),v=r?Ye(r):window,e){case"focusin":($n(v)||"true"===v.contentEditable)&&(rr=v,or=r,ar=null);break;case"focusout":ar=or=rr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,ir(l,n,o);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(l,n,o)}var b;if(xn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else _n?Cn(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(Nn&&"ko"!==n.locale&&(_n||"onCompositionStart"!==y?"onCompositionEnd"===y&&_n&&(b=Vt()):(Ut="value"in(It=o)?It.value:It.textContent,_n=!0)),0<(v=Au(r,y)).length&&(y=new sn(y,e,null,n,o),l.push({event:y,listeners:v}),b?y.data=b:null!==(b=Pn(n))&&(y.data=b))),(b=Sn?function(e,t){switch(e){case"compositionend":return Pn(t);case"keypress":return 32!==t.which?null:(jn=!0,En);case"textInput":return(e=t.data)===En&&jn?null:e;default:return null}}(e,n):function(e,t){if(_n)return"compositionend"===e||!xn&&Cn(e,t)?(e=Vt(),Bt=Ut=It=null,_n=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(y=Au(r,"onBeforeInput")).length&&(v=new sn("onBeforeInput","beforeinput",null,n,o),l.push({event:v,listeners:y}),v.data=b)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var a=ku((o[Re]||null).action),l=r.submitter;l&&null!==(t=(t=l[Re]||null)?ku(t.formAction):l.getAttribute("formAction"))&&(a=t,l=null);var i=new Jt("action","action",null,r,o);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==mu){var e=l?Su(o,l):new FormData(o);dl(n,{pending:!0,data:e,method:o.method,action:a},null,e)}}else"function"===typeof a&&(i.preventDefault(),e=l?Su(o,l):new FormData(o),dl(n,{pending:!0,data:e,method:o.method,action:a},a,e))},currentTarget:o}]})}}(l,e,r,n,o)}Pu(l,t)}))}function Fu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Au(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5!==(o=o.tag)&&26!==o&&27!==o||null===a||(null!=(o=Mt(e,n))&&r.unshift(Fu(e,o,a)),null!=(o=Mt(e,t))&&r.push(Fu(e,o,a))),e=e.return}return r}function Mu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Du(e,t,n,r,o){for(var a=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,c=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===c||(s=c,o?null!=(c=Mt(n,a))&&l.unshift(Fu(n,c,s)):o||null!=(c=Mt(n,a))&&l.push(Fu(n,c,s))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Ru=/\r\n?/g,Iu=/\u0000|\uFFFD/g;function Uu(e){return("string"===typeof e?e:""+e).replace(Ru,"\n").replace(Iu,"")}function Bu(e,t){return t=Uu(t),Uu(e)===t}function Vu(){}function Wu(e,t,n,r,o,a){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":it(e,"class",r);break;case"tabIndex":it(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":it(e,n,r);break;case"style":Et(e,r,a);break;case"data":if("object"!==t){it(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Wu(e,t,"name",o.name,o,null),Wu(e,t,"formEncType",o.formEncType,o,null),Wu(e,t,"formMethod",o.formMethod,o,null),Wu(e,t,"formTarget",o.formTarget,o,null)):(Wu(e,t,"encType",o.encType,o,null),Wu(e,t,"method",o.method,o,null),Wu(e,t,"target",o.target,o,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Vu);break;case"onScroll":null!=r&&_u("scroll",e);break;case"onScrollEnd":null!=r&&_u("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(l(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=_t(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":_u("beforetoggle",e),_u("toggle",e),lt(e,"popover",r);break;case"xlinkActuate":st(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":st(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":st(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":st(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":st(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":st(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":st(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":st(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":st(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":lt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&lt(e,n=Ct.get(n)||n,r)}}function Hu(e,t,n,r,o,a){switch(n){case"style":Et(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(l(60));e.innerHTML=n}}break;case"children":"string"===typeof r?kt(e,r):("number"===typeof r||"bigint"===typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&_u("scroll",e);break;case"onScrollEnd":null!=r&&_u("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ze.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),"function"===typeof(a=null!=(a=e[Re]||null)?a[n]:null)&&e.removeEventListener(t,a,o),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):lt(e,n,r):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o)))}}function qu(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_u("error",e),_u("load",e);var r,o=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":o=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Wu(e,t,r,i,n,null)}}return a&&Wu(e,t,"srcSet",n.srcSet,n,null),void(o&&Wu(e,t,"src",n.src,n,null));case"input":_u("invalid",e);var s=r=i=a=null,c=null,u=null;for(o in n)if(n.hasOwnProperty(o)){var d=n[o];if(null!=d)switch(o){case"name":a=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(l(137,t));break;default:Wu(e,t,o,d,n,null)}}return vt(e,r,s,c,u,i,a,!1),void dt(e);case"select":for(a in _u("invalid",e),o=i=r=null,n)if(n.hasOwnProperty(a)&&null!=(s=n[a]))switch(a){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":o=s;default:Wu(e,t,a,s,n,null)}return t=r,n=i,e.multiple=!!o,void(null!=t?yt(e,!!o,t,!1):null!=n&&yt(e,!!o,n,!0));case"textarea":for(i in _u("invalid",e),r=a=o=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":o=s;break;case"defaultValue":a=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(l(91));break;default:Wu(e,t,i,s,n,null)}return xt(e,o,a,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(o=n[c]))if("selected"===c)e.selected=o&&"function"!==typeof o&&"symbol"!==typeof o;else Wu(e,t,c,o,n,null);return;case"dialog":_u("cancel",e),_u("close",e);break;case"iframe":case"object":_u("load",e);break;case"video":case"audio":for(o=0;o<ju.length;o++)_u(ju[o],e);break;case"image":_u("error",e),_u("load",e);break;case"details":_u("toggle",e);break;case"embed":case"source":case"link":_u("error",e),_u("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(o=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Wu(e,t,u,o,n,null)}return;default:if(jt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(o=n[d])&&Hu(e,t,d,o,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(o=n[s])&&Wu(e,t,s,o,n,null))}var Qu=null,Ku=null;function Yu(e){return 9===e.nodeType?e:e.ownerDocument}function Gu(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Xu(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function Ju(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var Zu=null;var ed="function"===typeof setTimeout?setTimeout:void 0,td="function"===typeof clearTimeout?clearTimeout:void 0,nd="function"===typeof Promise?Promise:void 0,rd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof nd?function(e){return nd.resolve(null).then(e).catch(od)}:ed;function od(e){setTimeout((function(){throw e}))}function ad(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void gf(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);gf(t)}function ld(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ld(n),qe(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function id(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}function sd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function cd(e,t,n){switch(t=Yu(n),e){case"html":if(!(e=t.documentElement))throw Error(l(452));return e;case"head":if(!(e=t.head))throw Error(l(453));return e;case"body":if(!(e=t.body))throw Error(l(454));return e;default:throw Error(l(451))}}var ud=new Map,dd=new Set;function fd(e){return"function"===typeof e.getRootNode?e.getRootNode():e.ownerDocument}var pd=B.d;B.d={f:function(){var e=pd.f(),t=Dc();return e||t},r:function(e){var t=Ke(e);null!==t&&5===t.tag&&"form"===t.type?pl(t):pd.r(e)},D:function(e){pd.D(e),hd("dns-prefetch",e,null)},C:function(e,t){pd.C(e,t),hd("preconnect",e,t)},L:function(e,t,n){pd.L(e,t,n);var r=md;if(r&&e&&t){var o='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(o+='[imagesizes="'+ht(n.imageSizes)+'"]')):o+='[href="'+ht(e)+'"]';var a=o;switch(t){case"style":a=vd(e);break;case"script":a=wd(e)}ud.has(a)||(e=$({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),ud.set(a,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(bd(a))||"script"===t&&r.querySelector(xd(a))||(qu(t=r.createElement("link"),"link",e),Xe(t),r.head.appendChild(t)))}},m:function(e,t){pd.m(e,t);var n=md;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',a=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=wd(e)}if(!ud.has(a)&&(e=$({rel:"modulepreload",href:e},t),ud.set(a,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(xd(a)))return}qu(r=n.createElement("link"),"link",e),Xe(r),n.head.appendChild(r)}}},X:function(e,t){pd.X(e,t);var n=md;if(n&&e){var r=Ge(n).hoistableScripts,o=wd(e),a=r.get(o);a||((a=n.querySelector(xd(o)))||(e=$({src:e,async:!0},t),(t=ud.get(o))&&Ed(e,t),Xe(a=n.createElement("script")),qu(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}},S:function(e,t,n){pd.S(e,t,n);var r=md;if(r&&e){var o=Ge(r).hoistableStyles,a=vd(e);t=t||"default";var l=o.get(a);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(bd(a)))i.loading=5;else{e=$({rel:"stylesheet",href:e,"data-precedence":t},n),(n=ud.get(a))&&Nd(e,n);var s=l=r.createElement("link");Xe(s),qu(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){i.loading|=1})),s.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Sd(l,t,r)}l={type:"stylesheet",instance:l,count:1,state:i},o.set(a,l)}}},M:function(e,t){pd.M(e,t);var n=md;if(n&&e){var r=Ge(n).hoistableScripts,o=wd(e),a=r.get(o);a||((a=n.querySelector(xd(o)))||(e=$({src:e,async:!0,type:"module"},t),(t=ud.get(o))&&Ed(e,t),Xe(a=n.createElement("script")),qu(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}}};var md="undefined"===typeof document?null:document;function hd(e,t,n){var r=md;if(r&&"string"===typeof t&&t){var o=ht(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"===typeof n&&(o+='[crossorigin="'+n+'"]'),dd.has(o)||(dd.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(qu(t=r.createElement("link"),"link",e),Xe(t),r.head.appendChild(t)))}}function gd(e,t,n,r){var o,a,i,s,c=(c=X.current)?fd(c):null;if(!c)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=vd(n.href),(r=(n=Ge(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=vd(n.href);var u=Ge(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(bd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),ud.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},ud.set(e,n),u||(o=c,a=e,i=n,s=d.state,o.querySelector('link[rel="preload"][as="style"]['+a+"]")?s.loading=1:(a=o.createElement("link"),s.preload=a,a.addEventListener("load",(function(){return s.loading|=1})),a.addEventListener("error",(function(){return s.loading|=2})),qu(a,"link",i),Xe(a),o.head.appendChild(a))))),t&&null===r)throw Error(l(528,""));return d}if(t&&null!==r)throw Error(l(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=wd(n),(r=(n=Ge(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function vd(e){return'href="'+ht(e)+'"'}function bd(e){return'link[rel="stylesheet"]['+e+"]"}function yd(e){return $({},e,{"data-precedence":e.precedence,precedence:null})}function wd(e){return'[src="'+ht(e)+'"]'}function xd(e){return"script[async]"+e}function kd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,Xe(r),r;var o=$({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Xe(r=(e.ownerDocument||e).createElement("style")),qu(r,"style",o),Sd(r,n.precedence,e),t.instance=r;case"stylesheet":o=vd(n.href);var a=e.querySelector(bd(o));if(a)return t.state.loading|=4,t.instance=a,Xe(a),a;r=yd(n),(o=ud.get(o))&&Nd(r,o),Xe(a=(e.ownerDocument||e).createElement("link"));var i=a;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),qu(a,"link",r),t.state.loading|=4,Sd(a,n.precedence,e),t.instance=a;case"script":return a=wd(n.src),(o=e.querySelector(xd(a)))?(t.instance=o,Xe(o),o):(r=n,(o=ud.get(a))&&Ed(r=$({},n),o),Xe(o=(e=e.ownerDocument||e).createElement("script")),qu(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(l(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Sd(r,n.precedence,e));return t.instance}function Sd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,a=o,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===t)a=i;else if(a!==o)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Nd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Ed(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var jd=null;function Cd(e,t,n){if(null===jd){var r=new Map,o=jd=new Map;o.set(n,r)}else(r=(o=jd).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var a=n[o];if(!(a[He]||a[De]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var l=a.getAttribute(t)||"";l=e+l;var i=r.get(l);i?i.push(a):r.set(l,[a])}}return r}function Pd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function _d(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Td=null;function $d(){}function Ld(){if(this.count--,0===this.count)if(this.stylesheets)Od(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var zd=null;function Od(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,zd=new Map,t.forEach(Fd,e),zd=null,Ld.call(e))}function Fd(e,t){if(!(4&t.state.loading)){var n=zd.get(e);if(n)var r=n.get(null);else{n=new Map,zd.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<o.length;a++){var l=o[a];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(n.set(l.dataset.precedence,l),r=l)}r&&n.set(null,r)}l=(o=t.instance).getAttribute("data-precedence"),(a=n.get(l)||r)===r&&n.set(null,o),n.set(l,o),this.count++,r=Ld.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),a?a.parentNode.insertBefore(o,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Ad={$$typeof:g,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function Md(e,t,n,r,o,a,l,i){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=$e(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$e(0),this.hiddenUpdates=$e(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=a,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Dd(e,t,n,r,o,a,l,i,s,c,u,d){return e=new Md(e,t,n,l,i,s,c,d),t=1,!0===a&&(t|=24),a=Fs(3,null,null,t),e.current=a,a.stateNode=e,(t=Uo()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},_i(a),e}function Rd(e){return e?e=Lr:Lr}function Id(e,t,n,r,o,a){o=Rd(o),null===r.context?r.context=o:r.pendingContext=o,(r=$i(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=Li(e,r,t))&&(Lc(n,0,t),zi(n,e,t))}function Ud(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Bd(e,t){Ud(e,t),(e=e.alternate)&&Ud(e,t)}function Vd(e){if(13===e.tag){var t=_r(e,67108864);null!==t&&Lc(t,0,67108864),Bd(e,67108864)}}var Wd=!0;function Hd(e,t,n,r){var o=T.T;T.T=null;var a=B.p;try{B.p=2,Qd(e,t,n,r)}finally{B.p=a,T.T=o}}function qd(e,t,n,r){var o=T.T;T.T=null;var a=B.p;try{B.p=8,Qd(e,t,n,r)}finally{B.p=a,T.T=o}}function Qd(e,t,n,r){if(Wd){var o=Kd(r);if(null===o)Ou(e,t,r,Yd,n),lf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Zd=sf(Zd,e,t,n,r,o),!0;case"dragenter":return ef=sf(ef,e,t,n,r,o),!0;case"mouseover":return tf=sf(tf,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return nf.set(a,sf(nf.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,rf.set(a,sf(rf.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(lf(e,r),4&t&&-1<af.indexOf(e)){for(;null!==o;){var a=Ke(o);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var l=Ee(a.pendingLanes);if(0!==l){var i=a;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var s=1<<31-we(l);i.entanglements[1]|=s,l&=~s}hu(a),0===(6&ec)&&(wc=se()+500,gu(0,!1))}}break;case 13:null!==(i=_r(a,2))&&Lc(i,0,2),Dc(),Bd(a,2)}if(null===(a=Kd(r))&&Ou(e,t,r,Yd,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Ou(e,t,r,null,n)}}function Kd(e){return Gd(e=$t(e))}var Yd=null;function Gd(e){if(Yd=null,null!==(e=Qe(e))){var t=M(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=D(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Yd=e,null}function Xd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ce()){case ue:return 2;case de:return 8;case fe:case pe:return 32;case me:return 268435456;default:return 32}default:return 32}}var Jd=!1,Zd=null,ef=null,tf=null,nf=new Map,rf=new Map,of=[],af="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lf(e,t){switch(e){case"focusin":case"focusout":Zd=null;break;case"dragenter":case"dragleave":ef=null;break;case"mouseover":case"mouseout":tf=null;break;case"pointerover":case"pointerout":nf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rf.delete(t.pointerId)}}function sf(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=Ke(t))&&Vd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function cf(e){var t=Qe(e.target);if(null!==t){var n=M(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=D(n)))return e.blockedOn=t,void function(e,t){var n=B.p;try{return B.p=e,t()}finally{B.p=n}}(e.priority,(function(){if(13===n.tag){var e=Tc(),t=_r(n,e);null!==t&&Lc(t,0,e),Bd(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function uf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kd(e.nativeEvent);if(null!==n)return null!==(t=Ke(n))&&Vd(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Tt=r,n.target.dispatchEvent(r),Tt=null,t.shift()}return!0}function df(e,t,n){uf(e)&&n.delete(t)}function ff(){Jd=!1,null!==Zd&&uf(Zd)&&(Zd=null),null!==ef&&uf(ef)&&(ef=null),null!==tf&&uf(tf)&&(tf=null),nf.forEach(df),rf.forEach(df)}function pf(e,t){e.blockedOn===t&&(e.blockedOn=null,Jd||(Jd=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,ff)))}var mf=null;function hf(e){mf!==e&&(mf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){mf===e&&(mf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if("function"!==typeof r){if(null===Gd(r||n))continue;break}var a=Ke(n);null!==a&&(e.splice(t,3),t-=3,dl(a,{pending:!0,data:o,method:n.method,action:r},r,o))}})))}function gf(e){function t(t){return pf(t,e)}null!==Zd&&pf(Zd,e),null!==ef&&pf(ef,e),null!==tf&&pf(tf,e),nf.forEach(t),rf.forEach(t);for(var n=0;n<of.length;n++){var r=of[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<of.length&&null===(n=of[0]).blockedOn;)cf(n),null===n.blockedOn&&of.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],a=n[r+1],l=o[Re]||null;if("function"===typeof a)l||hf(n);else if(l){var i=null;if(a&&a.hasAttribute("formAction")){if(o=a,l=a[Re]||null)i=l.formAction;else if(null!==Gd(o))continue}else i=l.action;"function"===typeof i?n[r+1]=i:(n.splice(r,3),r-=3),hf(n)}}}function vf(e){this._internalRoot=e}function bf(e){this._internalRoot=e}bf.prototype.render=vf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Id(t.current,Tc(),e,t,null,null)},bf.prototype.unmount=vf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;0===e.tag&&tu(),Id(e.current,2,null,e,null,null),Dc(),t[Ie]=null}},bf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ae();e={blockedOn:null,target:e,priority:t};for(var n=0;n<of.length&&0!==t&&t<of[n].priority;n++);of.splice(n,0,e),0===n&&cf(e)}};var yf=o.version;if("19.0.0"!==yf)throw Error(l(527,yf,"19.0.0"));B.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=M(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return R(o),e;if(a===r)return R(o),t;a=a.sibling}throw Error(l(188))}if(n.return!==r.return)n=o,r=a;else{for(var i=!1,s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i){for(s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?I(e):null)?null:e.stateNode};var wf={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:T,findFiberByHostInstance:Qe,reconcilerVersion:"19.0.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var xf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xf.isDisabled&&xf.supportsFiber)try{ve=xf.inject(wf),be=xf}catch(Sf){}}t.createRoot=function(e,t){if(!i(e))throw Error(l(299));var n=!1,r="",o=Fl,a=Al,s=Ml;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(o=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Dd(e,1,!1,null,0,n,r,o,a,s,0,null),e[Ie]=t.current,Lu(8===e.nodeType?e.parentNode:e),new vf(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(l(299));var r=!1,o="",a=Fl,s=Al,c=Ml,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Dd(e,1,!0,t,0,r,o,a,s,c,0,u)).context=Rd(null),n=t.current,(o=$i(r=Tc())).callback=null,Li(n,o,r),t.current.lanes=r,Le(t,r),hu(t),e[Ie]=t.current,Lu(e),new bf(t)},t.version="19.0.0"},43:(e,t,n)=>{e.exports=n(288)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var w=y.prototype=new b;w.constructor=y,h(w,v.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k={H:null,A:null,T:null,S:null},S=Object.prototype.hasOwnProperty;function N(e,t,r,o,a,l){return r=l.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:l}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(){}function _(e,t,o,a,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,c,u=!1;if(null===e)u=!0;else switch(i){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case f:return _((u=e._init)(e._payload),t,o,a,l)}}if(u)return l=l(e),u=""===a?"."+C(e,0):a,x(l)?(o="",null!=u&&(o=u.replace(j,"$&/")+"/"),_(l,t,o,"",(function(e){return e}))):null!=l&&(E(l)&&(s=l,c=o+(null==l.key||e&&e.key===l.key?"":(""+l.key).replace(j,"$&/")+"/")+u,l=N(s.type,c,void 0,0,0,s.props)),t.push(l)),1;u=0;var d,m=""===a?".":a+":";if(x(e))for(var h=0;h<e.length;h++)u+=_(a=e[h],t,o,i=m+C(a,h),l);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(a=e.next()).done;)u+=_(a=a.value,t,o,i=m+C(a,h++),l);else if("object"===i){if("function"===typeof e.then)return _(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,o,a,l);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function T(e,t,n){if(null==e)return e;var r=[],o=0;return _(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function $(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function z(){}t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=l,t.PureComponent=y,t.StrictMode=a,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.act=function(){throw Error("act(...) is not supported in production builds of React.")},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!S.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var l=Array(a),i=0;i<a;i++)l[i]=arguments[i+2];r.children=l}return N(e.type,o,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){for(var i=Array(l),s=0;s<l;s++)i[s]=arguments[s+2];o.children=i}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===o[r]&&(o[r]=l[r]);return N(e,a,void 0,0,0,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:$}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),o=k.S;null!==o&&o(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(z,L)}catch(a){L(a)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t){return k.H.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.0.0"},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},499:(e,t)=>{function n(e,t,n,r){return new(n||(n=Promise))((function(o,a){function l(e){try{s(r.next(e))}catch(e){a(e)}}function i(e){try{s(r.throw(e))}catch(e){a(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,i)}s((r=r.apply(e,t||[])).next())}))}function r(e,t){return window.addEventListener(e,t),Promise.resolve({success:!0,message:"Event listener added"})}function o(e,t){const n=new CustomEvent(e,{detail:t});return window.dispatchEvent(n),Promise.resolve({success:!0,message:"Message dispatched"})}function a(e){const t=window.atob(e),n=t.length,r=new Uint8Array(n);for(let o=0;o<n;o++)r[o]=t.charCodeAt(o);return r.buffer}function l(e){let t=new Uint8Array(e),n="";for(let r of t)n+=String.fromCharCode(r);return window.btoa(n)}function i(e){const t=[];for(const n of e){const e=Array.isArray(n)?n:[n];for(const n of e){const e=n instanceof HTMLElement?n:document.getElementById(n);e&&t.push(e)}}return t}let s;"function"==typeof SuppressedError&&SuppressedError;const c={},u=[],d={};function f(){window.NL_TOKEN&&sessionStorage.setItem("NL_TOKEN",window.NL_TOKEN);const e=h().split(".")[1],t=window.NL_GINJECTED||window.NL_CINJECTED?"127.0.0.1":window.location.hostname;s=new WebSocket(`ws://${t}:${window.NL_PORT}?connectToken=${e}`),function(){r("ready",(()=>n(this,void 0,void 0,(function*(){if(yield m(u),!window.NL_EXTENABLED)return;let e=yield p("extensions.getStats");for(let t of e.connected)o("extensionReady",t)})))),r("extClientConnect",(e=>{o("extensionReady",e.detail)})),window.NL_EXTENABLED&&r("extensionReady",(e=>n(this,void 0,void 0,(function*(){e.detail in d&&(yield m(d[e.detail]),delete d[e.detail])}))))}(),function(){s.addEventListener("message",(e=>{var t,n,r;const l=JSON.parse(e.data);l.id&&l.id in c?((null===(t=l.data)||void 0===t?void 0:t.error)?(c[l.id].reject(l.data.error),"NE_RT_INVTOKN"==l.data.error.code&&(s.close(),document.body.innerText="",document.write("<code>NE_RT_INVTOKN</code>: Neutralinojs application cannot execute native methods since <code>NL_TOKEN</code> is invalid."))):(null===(n=l.data)||void 0===n?void 0:n.success)&&c[l.id].resolve(l.data.hasOwnProperty("returnValue")?l.data.returnValue:l.data),delete c[l.id]):l.event&&("openedFile"==l.event&&"dataBinary"==(null===(r=null==l?void 0:l.data)||void 0===r?void 0:r.action)&&(l.data.data=a(l.data.data)),o(l.event,l.data))})),s.addEventListener("open",(e=>n(this,void 0,void 0,(function*(){o("ready")})))),s.addEventListener("close",(e=>n(this,void 0,void 0,(function*(){o("serverOffline",{code:"NE_CL_NSEROFF",message:"Neutralino server is offline. Try restarting the application"})})))),s.addEventListener("error",(e=>n(this,void 0,void 0,(function*(){document.body.innerText="",document.write("<code>NE_CL_IVCTOKN</code>: Neutralinojs application cannot connect with the framework core using <code>NL_TOKEN</code>.")}))))}()}function p(e,t){return new Promise(((n,r)=>{if((null==s?void 0:s.readyState)!=WebSocket.OPEN)return o={method:e,data:t,resolve:n,reject:r},void u.push(o);var o;const a="10000000-1000-4000-8000-100000000000".replace(/[018]/g,(e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16))),l=h();c[a]={resolve:n,reject:r},s.send(JSON.stringify({id:a,method:e,data:t,accessToken:l}))}))}function m(e){return n(this,void 0,void 0,(function*(){for(;e.length>0;){const t=e.shift();try{const e=yield p(t.method,t.data);t.resolve(e)}catch(e){t.reject(e)}}}))}function h(){return window.NL_TOKEN||sessionStorage.getItem("NL_TOKEN")||""}function g(e,t){return p("filesystem.writeBinaryFile",{path:e,data:l(t)})}var v=Object.freeze({__proto__:null,appendBinaryFile:function(e,t){return p("filesystem.appendBinaryFile",{path:e,data:l(t)})},appendFile:function(e,t){return p("filesystem.appendFile",{path:e,data:t})},copy:function(e,t,n){return p("filesystem.copy",Object.assign({source:e,destination:t},n))},createDirectory:function(e){return p("filesystem.createDirectory",{path:e})},createWatcher:function(e){return p("filesystem.createWatcher",{path:e})},getAbsolutePath:function(e){return p("filesystem.getAbsolutePath",{path:e})},getJoinedPath:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p("filesystem.getJoinedPath",{paths:t})},getNormalizedPath:function(e){return p("filesystem.getNormalizedPath",{path:e})},getOpenedFileInfo:function(e){return p("filesystem.getOpenedFileInfo",{id:e})},getPathParts:function(e){return p("filesystem.getPathParts",{path:e})},getPermissions:function(e){return p("filesystem.getPermissions",{path:e})},getRelativePath:function(e,t){return p("filesystem.getRelativePath",{path:e,base:t})},getStats:function(e){return p("filesystem.getStats",{path:e})},getUnnormalizedPath:function(e){return p("filesystem.getUnnormalizedPath",{path:e})},getWatchers:function(){return p("filesystem.getWatchers")},move:function(e,t){return p("filesystem.move",{source:e,destination:t})},openFile:function(e){return p("filesystem.openFile",{path:e})},readBinaryFile:function(e,t){return new Promise(((n,r)=>{p("filesystem.readBinaryFile",Object.assign({path:e},t)).then((e=>{n(a(e))})).catch((e=>{r(e)}))}))},readDirectory:function(e,t){return p("filesystem.readDirectory",Object.assign({path:e},t))},readFile:function(e,t){return p("filesystem.readFile",Object.assign({path:e},t))},remove:function(e){return p("filesystem.remove",{path:e})},removeWatcher:function(e){return p("filesystem.removeWatcher",{id:e})},setPermissions:function(e,t,n){return p("filesystem.setPermissions",Object.assign(Object.assign({path:e},t),{mode:n}))},updateOpenedFile:function(e,t,n){return p("filesystem.updateOpenedFile",{id:e,event:t,data:n})},writeBinaryFile:g,writeFile:function(e,t){return p("filesystem.writeFile",{path:e,data:t})}});function b(e,t){return p("os.execCommand",Object.assign({command:e},t))}var y=Object.freeze({__proto__:null,execCommand:b,getEnv:function(e){return p("os.getEnv",{key:e})},getEnvs:function(){return p("os.getEnvs")},getPath:function(e){return p("os.getPath",{name:e})},getSpawnedProcesses:function(){return p("os.getSpawnedProcesses")},open:function(e){return p("os.open",{url:e})},setTray:function(e){return p("os.setTray",e)},showFolderDialog:function(e,t){return p("os.showFolderDialog",Object.assign({title:e},t))},showMessageBox:function(e,t,n,r){return p("os.showMessageBox",{title:e,content:t,choice:n,icon:r})},showNotification:function(e,t,n){return p("os.showNotification",{title:e,content:t,icon:n})},showOpenDialog:function(e,t){return p("os.showOpenDialog",Object.assign({title:e},t))},showSaveDialog:function(e,t){return p("os.showSaveDialog",Object.assign({title:e},t))},spawnProcess:function(e,t){return p("os.spawnProcess",Object.assign({command:e},t))},updateSpawnedProcess:function(e,t,n){return p("os.updateSpawnedProcess",{id:e,event:t,data:n})}}),w=Object.freeze({__proto__:null,getArch:function(){return p("computer.getArch")},getCPUInfo:function(){return p("computer.getCPUInfo")},getDisplays:function(){return p("computer.getDisplays")},getKernelInfo:function(){return p("computer.getKernelInfo")},getMemoryInfo:function(){return p("computer.getMemoryInfo")},getMousePosition:function(){return p("computer.getMousePosition")},getOSInfo:function(){return p("computer.getOSInfo")}}),x=Object.freeze({__proto__:null,getData:function(e){return p("storage.getData",{key:e})},getKeys:function(){return p("storage.getKeys")},setData:function(e,t){return p("storage.setData",{key:e,data:t})}});function k(e,t){return p("debug.log",{message:e,type:t})}var S=Object.freeze({__proto__:null,log:k});function N(e){return p("app.exit",{code:e})}var E=Object.freeze({__proto__:null,broadcast:function(e,t){return p("app.broadcast",{event:e,data:t})},exit:N,getConfig:function(){return p("app.getConfig")},killProcess:function(){return p("app.killProcess")},readProcessInput:function(e){return p("app.readProcessInput",{readAll:e})},restartProcess:function(e){return new Promise((t=>n(this,void 0,void 0,(function*(){let n=window.NL_ARGS.reduce(((e,t)=>(t.includes(" ")&&(t=`"${t}"`),e+" "+t)),"");(null==e?void 0:e.args)&&(n+=" "+e.args),yield b(n,{background:!0}),N(),t()}))))},writeProcessError:function(e){return p("app.writeProcessError",{data:e})},writeProcessOutput:function(e){return p("app.writeProcessOutput",{data:e})}});const j=new Set,C=new Map,P=new Map;function _(){return p("window.beginDrag",{screenX:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,screenY:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0})}function T(){return p("window.getSize")}var $=Object.freeze({__proto__:null,beginDrag:_,center:function(){return p("window.center")},create:function(e,t){return new Promise(((n,r)=>{function o(e){return"string"!=typeof e||(e=e.trim()).includes(" ")&&(e=`"${e}"`),e}t=Object.assign(Object.assign({},t),{useSavedState:!1});let a=window.NL_ARGS.reduce(((e,t,n)=>((t.includes("--path=")||t.includes("--debug-mode")||t.includes("--load-dir-res")||0==n)&&(e+=" "+o(t)),e)),"");a+=" --url="+o(e);for(let e in t)"processArgs"!=e&&(a+=` --window${"-"+e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}=${o(t[e])}`);t&&t.processArgs&&(a+=" "+t.processArgs),b(a,{background:!0}).then((e=>{n(e)})).catch((e=>{r(e)}))}))},exitFullScreen:function(){return p("window.exitFullScreen")},focus:function(){return p("window.focus")},getPosition:function(){return p("window.getPosition")},getSize:T,getTitle:function(){return p("window.getTitle")},hide:function(){return p("window.hide")},isFullScreen:function(){return p("window.isFullScreen")},isMaximized:function(){return p("window.isMaximized")},isMinimized:function(){return p("window.isMinimized")},isVisible:function(){return p("window.isVisible")},maximize:function(){return p("window.maximize")},minimize:function(){return p("window.minimize")},move:function(e,t){return p("window.move",{x:e,y:t})},print:function(){return p("window.print")},setAlwaysOnTop:function(e){return p("window.setAlwaysOnTop",{onTop:e})},setDraggableRegion:function(e,t){return new Promise(((r,o)=>{var a;const l=e instanceof HTMLElement?e:document.getElementById(e);if(!l)return o({code:"NE_WD_DOMNOTF",message:"Unable to find DOM element"});if(j.has(l))return o({code:"NE_WD_ALRDREL",message:"This DOM element is already an active draggable region"});if(null===(a=null==t?void 0:t.exclude)||void 0===a?void 0:a.length){const e=new Set;for(const n of t.exclude){const t=n instanceof HTMLElement?n:document.getElementById(n);t&&e.add(t)}e.size&&C.set(l,e)}const s=(c=l,function(e){return n(this,void 0,void 0,(function*(){if(0!==e.button)return;const t=C.get(c);if(t)for(const n of t)if(n.contains(e.target))return;yield _(e.screenX,e.screenY),e.preventDefault()}))});var c;l.addEventListener("pointerdown",s),j.add(l),P.set(l,s),r({success:!0,message:"Draggable region was activated",exclusions:{add(){if(!j.has(l))throw{code:"NE_WD_NOTDRRE",message:"DOM element is no longer an active draggable region. You likely called unsetDraggableRegion on this element too early!"};let e=C.get(l);e||(e=new Set,C.set(l,e));for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const o=i(n);for(const a of o)e.add(a)},remove(){if(!j.has(l))throw{code:"NE_WD_NOTDRRE",message:"DOM element is no longer an active draggable region. You likely called unsetDraggableRegion on this element too early!"};const e=C.get(l);if(!e)return;for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const o=i(n);for(const a of o)e.delete(a)},removeAll(){if(!j.has(l))throw{code:"NE_WD_NOTDRRE",message:"DOM element is no longer an active draggable region. You likely called unsetDraggableRegion on this element too early!"};C.delete(l)}}})}))},setFullScreen:function(){return p("window.setFullScreen")},setIcon:function(e){return p("window.setIcon",{icon:e})},setMainMenu:function(e){return p("window.setMainMenu",e)},setSize:function(e){return new Promise(((t,r)=>n(this,void 0,void 0,(function*(){let n=yield T();p("window.setSize",e=Object.assign(Object.assign({},n),e)).then((e=>{t(e)})).catch((e=>{r(e)}))}))))},setTitle:function(e){return p("window.setTitle",{title:e})},show:function(){return p("window.show")},snapshot:function(e){return p("window.snapshot",{path:e})},unmaximize:function(){return p("window.unmaximize")},unminimize:function(){return p("window.unminimize")},unsetDraggableRegion:function(e){return new Promise(((t,n)=>{const r=e instanceof HTMLElement?e:document.getElementById(e);if(!r)return n({code:"NE_WD_DOMNOTF",message:"Unable to find DOM element"});if(!j.has(r))return n({code:"NE_WD_NOTDRRE",message:"DOM element is not an active draggable region"});const o=P.get(r);o&&(r.removeEventListener("pointerdown",o),P.delete(r)),j.delete(r),C.delete(r),t({success:!0,message:"Draggable region was deactivated"})}))}}),L=Object.freeze({__proto__:null,broadcast:function(e,t){return p("events.broadcast",{event:e,data:t})},dispatch:o,off:function(e,t){return window.removeEventListener(e,t),Promise.resolve({success:!0,message:"Event listener removed"})},on:r});function z(){return p("extensions.getStats")}var O=Object.freeze({__proto__:null,broadcast:function(e,t){return p("extensions.broadcast",{event:e,data:t})},dispatch:function(e,t,r){return new Promise(((o,a)=>n(this,void 0,void 0,(function*(){const n=yield z();if(n.loaded.includes(e))if(n.connected.includes(e))try{const n=yield p("extensions.dispatch",{extensionId:e,event:t,data:r});o(n)}catch(n){a(n)}else!function(e,t){e in d?d[e].push(t):d[e]=[t]}(e,{method:"extensions.dispatch",data:{extensionId:e,event:t,data:r},resolve:o,reject:a});else a({code:"NE_EX_EXTNOTL",message:`${e} is not loaded`})}))))},getStats:z});let F=null;var A=Object.freeze({__proto__:null,checkForUpdates:function(e){return new Promise(((t,r)=>n(this,void 0,void 0,(function*(){if(!e)return r({code:"NE_RT_NATRTER",message:"Missing require parameter: url"});try{const n=yield fetch(e);F=JSON.parse(yield n.text()),function(e){return!!(e.applicationId&&e.applicationId==window.NL_APPID&&e.version&&e.resourcesURL)}(F)?t(F):r({code:"NE_UP_CUPDMER",message:"Invalid update manifest or mismatching applicationId"})}catch(n){r({code:"NE_UP_CUPDERR",message:"Unable to fetch update manifest"})}}))))},install:function(){return new Promise(((e,t)=>n(this,void 0,void 0,(function*(){if(!F)return t({code:"NE_UP_UPDNOUF",message:"No update manifest loaded"});try{const t=yield fetch(F.resourcesURL),n=yield t.arrayBuffer();yield g(window.NL_PATH+"/resources.neu",n),e({success:!0,message:"Update installed. Restart the process to see updates"})}catch(n){t({code:"NE_UP_UPDINER",message:"Update installation error"})}}))))}}),M=Object.freeze({__proto__:null,clear:function(){return p("clipboard.clear")},getFormat:function(){return p("clipboard.getFormat")},readHTML:function(){return p("clipboard.readHTML")},readImage:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new Promise(((t,n)=>{p("clipboard.readImage").then((n=>{if(n){const r=window.atob(n.data);let o,a,l,i=32==n.bpp?4:3;switch(e.toLowerCase()){case"rgb":o=n.width*n.height*3,a=[0,1,2];break;case"rgba":o=n.width*n.height*4,a=[0,1,2,3];break;case"argb":o=n.width*n.height*4,a=[3,0,1,2];break;case"bgra":o=n.width*n.height*4,a=[2,1,0,3];break;default:o=r.length,l=new Uint8Array(o);for(let e=0;e<o;e++)l[e]=r.charCodeAt(e);return n.data=l,void t(n)}l=new Uint8Array(o);let s,c,u,d,f,p=255==new Uint8Array(new Uint32Array([255]).buffer)[0],m=[],h=0;for(let e=0;e<r.length;e+=i)s=r.charCodeAt(e),c=r.charCodeAt(e+1),u=r.charCodeAt(e+2),d=4==i?r.charCodeAt(e+3):255,f=p?(d<<24|u<<16|c<<8|s)>>>0:(s<<24|c<<16|u<<8|d)>>>0,m=[f>>n.redShift&255,f>>n.greenShift&255,f>>n.blueShift&255,f>>n.alphaShift&255],a.forEach(((e,t)=>{l[t+h]=m[e]})),h+=a.length;n.data=l}t(n)})).catch((e=>{n(e)}))}))},readText:function(){return p("clipboard.readText")},writeHTML:function(e){return p("clipboard.writeHTML",{data:e})},writeImage:function(e){const t=Object.assign({},e);return(null==e?void 0:e.data)&&(t.data=l(e.data)),p("clipboard.writeImage",t)},writeText:function(e){return p("clipboard.writeText",{data:e})}}),D=Object.freeze({__proto__:null,extractDirectory:function(e,t){return p("resources.extractDirectory",{path:e,destination:t})},extractFile:function(e,t){return p("resources.extractFile",{path:e,destination:t})},getFiles:function(){return p("resources.getFiles")},getStats:function(e){return p("resources.getStats",{path:e})},readBinaryFile:function(e){return new Promise(((t,n)=>{p("resources.readBinaryFile",{path:e}).then((e=>{t(a(e))})).catch((e=>{n(e)}))}))},readFile:function(e){return p("resources.readFile",{path:e})}}),R=Object.freeze({__proto__:null,getMounts:function(){return p("server.getMounts")},mount:function(e,t){return p("server.mount",{path:e,target:t})},unmount:function(e){return p("server.unmount",{path:e})}}),I=Object.freeze({__proto__:null,getMethods:function(){return p("custom.getMethods")}});let U=!1;t.app=E,t.clipboard=M,t.computer=w,t.custom=I,t.debug=S,t.events=L,t.extensions=O,t.filesystem=v,t.init=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e=Object.assign({exportCustomMethods:!0},e),!U){if(f(),window.NL_ARGS.find((e=>"--neu-dev-auto-reload"==e))&&r("neuDev_reloadApp",(()=>n(this,void 0,void 0,(function*(){yield k("Reloading the application..."),location.reload()})))),e.exportCustomMethods&&window.NL_CMETHODS&&window.NL_CMETHODS.length>0)for(const e of window.NL_CMETHODS)Neutralino.custom[e]=function(){let t={};for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];for(const[e,a]of r.entries())t="object"!=typeof a||Array.isArray(a)||null==a?Object.assign(Object.assign({},t),{["arg"+e]:a}):Object.assign(Object.assign({},t),a);return p("custom."+e,t)};window.NL_CVERSION="6.2.0",window.NL_CCOMMIT="425c526c318342e0e5d0f17caceef2a53049eda4",U=!0}},t.os=y,t.resources=D,t.server=R,t.storage=x,t.updater=A,t.window=$},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var l={d:{f:a,r:function(){throw Error(o(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},i=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(o(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=l.p;try{if(s.T=null,l.p=2,e)return e()}finally{s.T=t,l.p=n,l.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,l.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&l.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),o="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?l.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:o,fetchPriority:a}):"script"===n&&l.d.X(e,{crossOrigin:r,integrity:o,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);l.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&l.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);l.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);l.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else l.d.m(e)},t.requestFormReset=function(e){l.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.0.0"},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(e,t,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),"key"in t)for(var a in r={},t)"key"!==a&&(r[a]=t[a]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:o,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=o,t.jsxs=o},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,l=o>>>1;r<l;){var i=2*(r+1)-1,s=e[i],c=i+1,u=e[c];if(0>a(s,n))c<o&&0>a(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[i]=n,r=i);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var c=[],u=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,y="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(g=!1,w(e),!h)if(null!==r(c))h=!0,$();else{var t=r(u);null!==t&&L(x,t.startTime-e)}}var k,S=!1,N=-1,E=5,j=-1;function C(){return!(t.unstable_now()-j<E)}function P(){if(S){var e=t.unstable_now();j=e;var n=!0;try{e:{h=!1,g&&(g=!1,b(N),N=-1),m=!0;var a=p;try{t:{for(w(e),f=r(c);null!==f&&!(f.expirationTime>e&&C());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof i){f.callback=i,w(e),n=!0;break t}f===r(c)&&o(c),w(e)}else o(c);f=r(c)}if(null!==f)n=!0;else{var s=r(u);null!==s&&L(x,s.startTime-e),n=!1}}break e}finally{f=null,p=a,m=!1}n=void 0}}finally{n?k():S=!1}}}if("function"===typeof y)k=function(){y(P)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,T=_.port2;_.port1.onmessage=P,k=function(){T.postMessage(null)}}else k=function(){v(P,0)};function $(){S||(S=!0,k())}function L(e,n){N=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,$())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var l=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?l+a:l:a=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:i=a+i,sortIndex:-1},a>l?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(b(N),N=-1):g=!0,L(x,a-l))):(e.sortIndex=i,n(c,e),h||m||(h=!0,$())),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}var r=n(43),o=n(391),a=n(499);const l=6e4,i=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Operation timed out";const o=new Promise(((e,o)=>{t=setTimeout((()=>o(new Error(r))),n)}));return Promise.race([e,o]).finally((()=>clearTimeout(t)))},s=e=>new Promise((t=>setTimeout(t,e)));const c=new class{constructor(){this.spawnedProcesses=new Map}isNeutralino(){return!!a.init()&&!!a}async getUserProfilePath(){try{console.debug("[NeuService] Fetching user profile path...");let e=await a.os.getEnv("USER")||await a.os.getEnv("USERPROFILE");if(!e)throw new Error("User profile path not found");return console.info("[NeuService] User profile path:",e),e}catch(e){throw console.error("[NeuService] Error fetching user profile path:",e.message),e}}async getOSInfo(){if(this._cachedOSInfo)return this._cachedOSInfo;try{console.debug("[NeuService] Detecting operating system...");const e=await i(a.computer.getOSInfo(),5e3,"OS detection timeout");let t=e.name.toLowerCase();t.includes("win")?t="windows":t.includes("mac")||t.includes("darwin")?t="mac":(console.warn(`[NeuService] Unrecognized OS: ${e.name}, defaulting to 'windows'`),t="windows");const n={name:t};return this._cachedOSInfo=n,console.info(`[NeuService] Detected OS: ${t}`),n}catch(e){console.error("[NeuService] Error detecting operating system:",e.message);const t={name:"windows"};return this._cachedOSInfo=t,t}}async move(e,t){try{console.debug("[NeuService] Moving window with position check"),await a.window.move(e,t)}catch(n){throw console.error("[NeuService] Error in move():",n),n}}async getWindowPosition(){try{console.debug("[NeuService] Getting window position");return await i(a.window.getPosition(),l,"Window position timeout")}catch(e){throw console.error("[NeuService] Error getting window position:",e.message),e}}async exists(e){try{return await a.filesystem.getStats(e),!0}catch{return!1}}async getStats(e){try{return await i(a.filesystem.getStats(e),l,`Stats timeout: ${e}`)}catch(t){throw console.error(`[NeuService] Error getting stats for ${e}:`,t.message),t}}async readDirectory(e){try{console.debug(`[NeuService] Reading directory: ${e}`);const t=(await i(a.filesystem.readDirectory(e),l,`Directory read timeout: ${e}`)).map((e=>e.entry));return console.debug(`[NeuService] Directory read complete: ${e}, ${t.length} entries found`),t}catch(t){throw console.error(`[NeuService] Error reading directory ${e}:`,t.message),t}}async createDirectory(e){try{return await this.exists(e)?(console.debug(`[NeuService] Directory already exists: ${e}`),!0):(await a.filesystem.createDirectory(e),console.info(`[NeuService] Directory created: ${e}`),!0)}catch(t){return console.error(`[NeuService] Error creating directory ${e}:`,t.message),!1}}async readFile(e){try{return await i(a.filesystem.readFile(e),l,`Read timeout: ${e}`)}catch(t){return console.error(`[NeuService] Error reading file ${e}:`,t.message),null}}async readBinaryFile(e){try{console.debug(`[NeuService] Reading binary file: ${e}`);const t=await i(a.filesystem.readBinaryFile(e),l,`Binary read timeout: ${e}`);return console.debug(`[NeuService] Binary file read: ${e}`),t}catch(t){throw console.error(`[NeuService] Error reading binary file ${e}:`,t.message),t}}async writeFile(e,t){try{return await i(a.filesystem.writeFile(e,t),l,`Write timeout: ${e}`),console.info(`[NeuService] File written: ${e}`),!0}catch(n){return console.error(`[NeuService] Error writing file ${e}:`,n.message),!1}}async writeBinaryFile(e,t){try{return console.debug(`[NeuService] Writing binary file: ${e}`),await i(a.filesystem.writeBinaryFile(e,t),l,`Binary write timeout: ${e}`),console.info(`[NeuService] Binary file written: ${e}`),!0}catch(n){return console.error(`[NeuService] Error writing binary file ${e}:`,n.message),!1}}async writeBinaryFileArrayBuffer(e,t){try{console.debug(`[NeuService] Writing binary file (ArrayBuffer): ${e}, size: ${t.byteLength} bytes`);const n=t.byteLength/1048576;return n>10?await this.writeFileInChunks(e,t):(console.info(`[NeuService] Writing small file directly (${n.toFixed(2)}MB)`),await i(a.filesystem.writeBinaryFile(e,t),l,`Binary write timeout: ${e}`),console.info(`[NeuService] Binary file written (ArrayBuffer): ${e}`),!0)}catch(n){throw console.error(`[NeuService] Error writing binary file ${e}:`,n.message),n}}async writeBinaryFileSimple(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{const r=t.byteLength/1048576;return console.info(`[NeuService] Writing binary file using parallel approach (${r.toFixed(2)}MB): ${e}`),r>5?await this.writeBinaryFileParallel(e,t,n):(console.info("[NeuService] Using direct write for smaller file"),"function"===typeof n&&n(50),await i(a.filesystem.writeBinaryFile(e,t),l,`Binary write timeout: ${e}`),"function"===typeof n&&n(100),console.info(`[NeuService] Binary file written successfully: ${e}`),!0)}catch(r){throw console.error(`[NeuService] Error writing binary file ${e}:`,r.message),r}}async writeBinaryFileParallel(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{const r=t.byteLength/1048576;console.info(`[NeuService] Writing large file using parallel chunks (${r.toFixed(2)}MB): ${e}`),await a.filesystem.writeFile(e,"");const o=10485760,l=Math.ceil(t.byteLength/o),s=new Uint8Array(t);console.info(`[NeuService] Writing ${l} chunks of 10MB each in parallel batches`);const c=6,u=Math.ceil(l/c);for(let d=0;d<u;d++){const r=d*c,f=Math.min(r+c,l);console.info(`[NeuService] Processing batch ${d+1}/${u} (chunks ${r+1}-${f})`);const p=[];for(let n=r;n<f;n++){const r=n*o,c=Math.min(r+o,t.byteLength),u=c-r,d=s.slice(r,c),f=d.buffer.slice(d.byteOffset,d.byteOffset+d.byteLength),m=i(a.filesystem.appendBinaryFile(e,f),6e4,`Chunk write timeout: ${e}, chunk ${n+1}/${l}`).then((()=>{console.debug(`[NeuService] Chunk ${n+1}/${l} completed (${(u/1048576).toFixed(2)}MB)`)}));p.push(m)}await Promise.all(p);const m=Math.round((d+1)/u*100);console.info(`[NeuService] Batch ${d+1}/${u} completed (${m}%)`),"function"===typeof n&&n(m)}return console.info(`[NeuService] All parallel chunks written successfully: ${e}`),!0}catch(r){throw console.error(`[NeuService] Error writing parallel file ${e}:`,r.message),r}}async writeBinaryFileChunked(e,t){try{const n=t.byteLength/1048576;console.info(`[NeuService] Writing large file using chunked append (${n.toFixed(2)}MB): ${e}`),await a.filesystem.writeFile(e,"");const r=20971520,o=Math.ceil(t.byteLength/r),l=new Uint8Array(t);console.info(`[NeuService] Writing ${o} chunks of up to 20MB each`);for(let s=0;s<o;s++){const n=s*r,c=Math.min(n+r,t.byteLength),u=c-n,d=l.slice(n,c),f=d.buffer.slice(d.byteOffset,d.byteOffset+d.byteLength),p=Math.round((s+1)/o*100);console.info(`[NeuService] Writing chunk ${s+1}/${o} (${p}%), size: ${(u/1048576).toFixed(2)}MB`),await i(a.filesystem.appendBinaryFile(e,f),6e4,`Chunk write timeout: ${e}, chunk ${s+1}/${o}`)}return console.info(`[NeuService] All chunks written successfully: ${e}`),!0}catch(n){throw console.error(`[NeuService] Error writing chunked file ${e}:`,n.message),n}}async writeFileInChunks(e,t){try{console.info(`[NeuService] Writing large file in chunks: ${e}`);try{await this.remove(e)}catch(n){}await a.filesystem.writeFile(e,"");const r=5242880,o=Math.ceil(t.byteLength/r);console.info(`[NeuService] Writing ${o} chunks of 5MB each`);const s=new Uint8Array(t);for(let n=0;n<o;n++){const c=n*r,u=Math.min(c+r,t.byteLength),d=u-c,f=s.slice(c,u),p=f.buffer.slice(f.byteOffset,f.byteOffset+f.byteLength);console.info(`[NeuService] Writing chunk ${n+1}/${o}, size: ${(d/1048576).toFixed(2)}MB`),await i(a.filesystem.appendBinaryFile(e,p),l,`Chunk write timeout: ${e}, chunk ${n+1}/${o}`)}return console.info(`[NeuService] All chunks written successfully: ${e}`),!0}catch(n){throw console.error(`[NeuService] Error writing file in chunks ${e}:`,n.message),n}}async appendBinaryFile(e,t){try{return console.debug(`[NeuService] Appending to binary file: ${e}`),await i(a.filesystem.appendBinaryFile(e,t),l,`Binary append timeout: ${e}`),console.debug(`[NeuService] Append to binary file completed: ${e}`),!0}catch(n){throw console.error(`[NeuService] Error appending to binary file ${e}:`,n.message),n}}async appendBinaryFileArrayBuffer(e,t){try{return console.debug(`[NeuService] Appending binary file (ArrayBuffer): ${e}, size: ${t.byteLength} bytes`),await i(a.filesystem.appendBinaryFile(e,t),l,`Binary append timeout: ${e}`),console.debug(`[NeuService] Binary file appended (ArrayBuffer): ${e}`),!0}catch(n){throw console.error(`[NeuService] Error appending binary file ${e}:`,n.message),n}}async moveFile(e,t){try{return console.debug(`[NeuService] Moving file from ${e} to ${t}`),await i(a.filesystem.moveFile(e,t),l,`Move timeout: ${e} to ${t}`),console.info(`[NeuService] File moved: ${e} \u2192 ${t}`),!0}catch(n){throw console.error(`[NeuService] Error moving file from ${e} to ${t}:`,n.message),n}}async copyFile(e,t){try{return console.debug(`[NeuService] Copying file from ${e} to ${t}`),await i(a.filesystem.copyFile(e,t),l,`Copy timeout: ${e} to ${t}`),console.info(`[NeuService] File copied: ${e} \u2192 ${t}`),!0}catch(n){throw console.error(`[NeuService] Error copying file from ${e} to ${t}:`,n.message),n}}async removeFile(e){try{return console.debug(`[NeuService] Removing file: ${e}`),await i(a.filesystem.removeFile(e),l,`Remove file timeout: ${e}`),console.info(`[NeuService] File removed: ${e}`),!0}catch(t){throw console.error(`[NeuService] Error removing file ${e}:`,t.message),t}}async remove(e){try{return await this.exists(e)?(await i(a.filesystem.remove(e),l,`Remove timeout: ${e}`),console.info(`[NeuService] Path removed: ${e}`),!0):(console.debug(`[NeuService] Path does not exist: ${e}`),!0)}catch(t){return console.error(`[NeuService] Error removing path ${e}:`,t.message),!1}}async execCommand(e){try{var t,n;console.info(`[NeuService] Executing command: ${e}`);const r=await i(a.os.execCommand(e),l,`Command timeout: ${e}`);return console.debug("[NeuService] Command result:",{exitCode:r.exitCode,stdOut:null===(t=r.stdOut)||void 0===t?void 0:t.substring(0,200),stdErr:null===(n=r.stdErr)||void 0===n?void 0:n.substring(0,200)}),r}catch(r){throw console.error(`[NeuService] Error executing command ${e}:`,r.message),r}}async getEnv(e){try{return await i(a.os.getEnv(e),l,`Env timeout: ${e}`)}catch(t){return console.error(`[NeuService] Error fetching environment variable ${e}:`,t.message),null}}async checkForUpdates(e){try{console.debug(`[NeuService] Checking for updates from ${e}`);const t=await i(a.updater.checkForUpdates(e),l,`Update check timeout: ${e}`);return console.info("[NeuService] Update check result:",t),t}catch(t){throw console.error("[NeuService] Error checking for updates:",t.message),t}}async install(){try{console.debug("[NeuService] Starting update installation...");const e=await i(a.updater.install(),l,"Update install timeout");return console.info("[NeuService] Update installation result:",e),e}catch(e){throw console.error("[NeuService] Error installing update:",e.message),e}}async restart(){try{console.debug("[NeuService] Restarting application..."),await i(a.app.restartProcess(),l,"Restart timeout")}catch(e){throw console.error("[NeuService] Error restarting application:",e.message),e}}normalizeDirectoryPath(e){try{const t=e;if(!e)return console.warn("[NeuService] Received empty path for normalization"),"";for((e=(e=e.replace(/\\/g,"/")).replace(/\/+/g,"/")).match(/^[A-Za-z]:/)&&(2!==e.length&&"/"===e[2]||(e=e.substring(0,2)+"/"+e.substring(2)));e.length>3&&e.endsWith("/");)e=e.slice(0,-1);return console.debug(`[NeuService] Path normalized: "${t}" \u2192 "${e}"`),e}catch(t){return console.error("[NeuService] Error normalizing path:",t.message),e}}async spawnProcess(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{console.info(`[NeuService] Spawning process request: ${e}`);const n=e.replace(/\//g,"\\"),r={envs:{},...t};r.cwd?(r.cwd=this.normalizeDirectoryPath(r.cwd).replace(/\//g,"\\"),r.cwd.length>3&&r.cwd.endsWith("\\")&&(r.cwd=r.cwd.slice(0,-1)),console.debug(`[NeuService] Normalized CWD for spawn: "${r.cwd}"`)):console.debug("[NeuService] No CWD provided for spawn."),console.debug("[NeuService] Final parameters for Neutralino.os.spawnProcess:"),console.debug(`  Command: "${n}"`),console.debug(`  Options: ${JSON.stringify(r)}`);const o=await i(a.os.spawnProcess(n,r),l,`Spawn process timeout: ${n}`);return console.info(`[NeuService] Process spawned successfully with PID: ${o.pid}`),o.pid&&this.spawnedProcesses.set(o.pid,!0),o.success=!0,o}catch(n){throw console.error(`[NeuService] Error during Neutralino.os.spawnProcess for command "${e}":`,n.message),new Error(`Failed to spawn process "${e}". Native error: ${n.message}`)}}async runExecutable(e){try{console.debug(`[NeuService] Request to run executable: ${e}`);const n=e.replace(/\//g,"\\");let r=".";const o=n.lastIndexOf("\\");-1!==o?r=n.substring(0,o):console.warn(`[NeuService] Could not determine directory for executable: ${e}. Using default '.'`),console.debug(`[NeuService] Determined working directory for executable: "${r}"`),console.warn("[NeuService] WORKAROUND: Launching via execCommand(background: true).");const l=n,i={background:!0,cwd:r,envs:{}};console.debug("[NeuService] Final parameters for Neutralino.os.execCommand (background):"),console.debug(`  Command: "${l}"`),console.debug(`  Options: ${JSON.stringify(i)}`),await a.os.execCommand(l,i),console.info(`[NeuService] execCommand (background) called for ${l}.`);const c=n.substring(o+1);console.debug(`[NeuService] Starting process check loop for: ${c}`);const u=Date.now();let d=0;for(;Date.now()-u<6e3;)try{const e=await this.findProcessesByName(c);if(e.length>0)return d=e[0].pid,console.info(`[NeuService] Found process ${c} with PID ${d} after ${Date.now()-u}ms`),{success:!0,pid:d};await s(500)}catch(t){console.warn(`[NeuService] Error during process check: ${t.message}`)}return console.warn(`[NeuService] Process check timed out after 6000ms for ${c}`),{success:!0,pid:0}}catch(n){throw console.error(`[NeuService] Error running executable "${e}" via execCommand workaround:`,n.message),n}}async minimize(){try{console.debug("[NeuService] Minimizing application..."),await i(a.window.minimize(),l,"minimize timeout")}catch(e){throw console.error("[NeuService] Error hiding application:",e.message),e}}async isProcessRunning(e){try{var t;if(!e)return!1;console.debug(`[NeuService] Checking if process is running: PID ${e}`);let n="";n="windows"===(await this.getOSInfo()).name?`powershell -Command "& { $ErrorActionPreference = 'SilentlyContinue'; (Get-Process -Id ${e}).Id }"`:`ps -p ${e} -o pid=`;const r=await i(a.os.execCommand(n,{background:!1,envs:{}}),3e3,`Process check timeout for PID ${e}`),o=0===r.exitCode&&(null===(t=r.stdOut)||void 0===t?void 0:t.trim())===`${e}`;return console.debug(`[NeuService] Process ${e} is ${o?"running":"not running"}`),o}catch(n){return console.debug(`[NeuService] Process ${e} check failed (likely not running):`,n.message),!1}}async findProcessesByName(e){try{if(!e)return[];const t=e.replace(/\.exe$/i,"");console.debug(`[NeuService] Looking for processes with base name: ${t}`);let n="";const r=await this.getOSInfo();n="windows"===r.name?`powershell -Command "& { $ErrorActionPreference = 'SilentlyContinue'; Get-Process -Name '${t}' | Select-Object Name, Id | ConvertTo-Csv -NoTypeInformation }"`:`pgrep -if "${e}"`;const o=await i(a.os.execCommand(n,{background:!1,envs:{}}),3e3,`Process search timeout for ${e}`);let l=[];if(0===o.exitCode&&o.stdOut&&""!==o.stdOut.trim()){const n=o.stdOut.trim().split("\n");if("windows"===r.name)for(let r=1;r<n.length;r++){const o=n[r].trim();if(!o)continue;const a=o.match(/(?:\"([^\"]*)\"|([^,]+))/g);if(a&&a.length>=2){const n=a[0].replace(/\"/g,"").toLowerCase(),r=parseInt(a[1].replace(/\"/g,""));isNaN(r)||n!==t.toLowerCase()||l.push({name:e,pid:r})}}else for(const t of n){const n=parseInt(t.trim());isNaN(n)||l.push({name:e,pid:n})}}return console.debug(`[NeuService] Found ${l.length} processes matching ${e}`),l}catch(t){return console.debug(`[NeuService] Process search failed for ${e}:`,t.message),[]}}async setDraggableRegion(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{var n,r;console.debug(`[NeuService] Setting draggable region for element: ${"string"===typeof e?e:"HTMLElement"}`);const o={alwaysCapture:null!==(n=t.alwaysCapture)&&void 0!==n&&n,dragMinDistance:null!==(r=t.dragMinDistance)&&void 0!==r?r:10};await i(a.window.setDraggableRegion(e,o),l,"setDraggableRegion timeout"),console.info("[NeuService] Draggable region set successfully")}catch(o){throw console.error("[NeuService] Error setting draggable region:",o.message),o}}};var u=n(579);const d=e=>{let{currentTab:t,setCurrentTab:n,toolboxVersion:o,statusMessage:a="System Ready",updateNotificationCount:l=0}=e;(0,r.useRef)(0);const[i,s]=(0,r.useState)((()=>localStorage.getItem("pluto-theme")||"light"));return(0,r.useEffect)((()=>(document.documentElement.setAttribute("data-theme",i),console.info("[Titlebar] Using default window controls - draggable titlebar disabled"),()=>{})),[i]),(0,u.jsxs)("div",{className:"relative z-[999]",children:["      ",(0,u.jsxs)("div",{className:"relative bg-bg-base border-b border-border-l1",children:[(0,u.jsx)("div",{id:"titlebar",className:"relative h-full w-full px-8",children:(0,u.jsxs)("div",{className:"flex items-center justify-between h-[48px]",children:[(0,u.jsxs)("div",{className:"flex items-center gap-3",children:[(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-bg-surface/10 rounded-full blur-lg"}),(0,u.jsx)("img",{src:"https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/logo.png",alt:"Pluto Logo",className:"relative h-7 w-8"})]}),(0,u.jsxs)("div",{className:"flex flex-col",children:[(0,u.jsx)("div",{className:"flex items-center gap-2",children:(0,u.jsx)("span",{className:"text-sm font-bold text-span-default tracking-wide font-jakarta",children:"Pluto Toolbox"})}),(0,u.jsx)("span",{className:"text-[11px] text-span-muted",children:"MovieStarPlanet Extension"})]})]}),"            ",(0,u.jsxs)("div",{className:"flex items-center gap-2",children:["              ",(0,u.jsx)("div",{className:"flex items-center gap-2 px-3 py-1.5 rounded-md shadow-sm transform transition-all duration-200 group bg-bg-surface border border-border-l1",children:(0,u.jsxs)("span",{id:"toolbox-version",className:"text-span-default text-xs font-thin",children:["v",o]})}),(0,u.jsx)("a",{href:"https://discord.gg/dolo",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 px-3 py-1.5 rounded-md shadow-sm transform transition-all hover:scale-105 duration-200 group bg-bg-surface border border-border-l1",children:(0,u.jsx)("svg",{className:"w-4 h-4 text-span-default",viewBox:"0 0 71 55",fill:"currentColor",children:(0,u.jsx)("path",{d:"M60.1045 4.8978C55.5792 2.8214 50.7265 1.2916 45.6527 0.41542C45.5603 0.39851 45.468 0.440769 45.4204 0.525289C44.7963 1.6353 44.105 3.0834 43.6209 4.2216C38.1637 3.4046 32.7345 3.4046 27.3892 4.2216C26.905 3.0581 26.1886 1.6353 25.5617 0.525289C25.5141 0.443589 25.4218 0.40133 25.3294 0.41542C20.2584 1.2888 15.4057 2.8186 10.8776 4.8978C10.8384 4.9147 10.8048 4.9429 10.7825 4.9795C1.57795 18.7309 -0.943561 32.1443 0.293408 45.3914C0.299005 45.4562 0.335386 45.5182 0.385761 45.5576C6.45866 50.0174 12.3413 52.7249 18.1147 54.5195C18.2071 54.5477 18.305 54.5139 18.3638 54.4378C19.7295 52.5728 20.9469 50.6063 21.9907 48.5383C22.0523 48.4172 21.9935 48.2735 21.8676 48.2256C19.9366 47.4931 18.0979 46.6 16.3292 45.5858C16.1893 45.5041 16.1781 45.304 16.3068 45.2082C16.679 44.9293 17.0513 44.6391 17.4067 44.3461C17.471 44.2926 17.5606 44.2813 17.6362 44.3151C29.2558 49.6202 41.8354 49.6202 53.3179 44.3151C53.3935 44.2785 53.4831 44.2898 53.5502 44.3433C53.9057 44.6363 54.2779 44.9293 54.6529 45.2082C54.7816 45.304 54.7732 45.5041 54.6333 45.5858C52.8646 46.6197 51.0259 47.4931 49.0921 48.2228C48.9662 48.2707 48.9102 48.4172 48.9718 48.5383C50.038 50.6034 51.2554 52.5699 52.5959 54.435C52.6519 54.5139 52.7526 54.5477 52.845 54.5195C58.6464 52.7249 64.529 50.0174 70.6019 45.5576C70.6551 45.5182 70.6887 45.459 70.6943 45.3942C72.1747 30.0791 68.2147 16.7757 60.1968 4.9823C60.1772 4.9429 60.1437 4.9147 60.1045 4.8978ZM23.7259 37.3253C20.2276 37.3253 17.3451 34.1136 17.3451 30.1693C17.3451 26.225 20.1717 23.0133 23.7259 23.0133C27.308 23.0133 30.1626 26.2532 30.1066 30.1693C30.1066 34.1136 27.28 37.3253 23.7259 37.3253ZM47.3178 37.3253C43.8196 37.3253 40.9371 34.1136 40.9371 30.1693C40.9371 26.225 43.7636 23.0133 47.3178 23.0133C50.9 23.0133 53.7545 26.2532 53.6986 30.1693C53.6986 34.1136 50.9 37.3253 47.3178 37.3253Z"})})}),(0,u.jsx)("a",{href:"https://discord.gg/mspshop",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 px-3 py-1.5 rounded-md shadow-sm transform transition-all hover:scale-105 duration-200 group bg-bg-surface border border-border-l1",children:(0,u.jsxs)("svg",{className:"w-4 h-4 text-span-muted",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,u.jsx)("path",{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}),(0,u.jsx)("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),(0,u.jsx)("path",{d:"M16 10a4 4 0 0 1-8 0"})]})}),"              "]}),"          "]})}),(0,u.jsx)("div",{className:"relative px-8 bg-bg-surface border-t border-border-l1",children:(0,u.jsxs)("div",{className:"flex items-center justify-between h-[45px]",children:[(0,u.jsxs)("div",{className:"flex items-center gap-2",children:[(0,u.jsx)("button",{onClick:()=>n("tools"),className:`group relative px-3 py-2 text-xs font-medium \n                    rounded-md overflow-hidden focus:outline-none ${"tools"===t?"bg-bg-surface-hover text-span-default shadow-sm border border-border-l1 pointer-events-none":"hover:text-span-hover text-span-muted hover:bg-bg-hover border border-transparent"} \n                    transition-all duration-300`,children:(0,u.jsxs)("div",{className:"relative flex items-center gap-2",children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5 transform group-hover:scale-110 transition-transform duration-300 fill-current",xmlns:"http://www.w3.org/2000/svg",height:"24px",viewBox:"0 -960 960 960",width:"24px",children:(0,u.jsx)("path",{d:"M474-83q-174-26-276-134.5T82-513q-1-11 2.5-19.5T94-547q6-6 14.5-9t18.5-1q183 25 282 141t110 290q1 9-2.5 17.5T506-94q-6 6-14.5 9.5T474-83Zm6-372q-14-22-64.5-64.5T330-576q8-50 40.5-126.5T448-841q6-8 14.5-12t17.5-4q9 0 17 4t14 13q45 63 78.5 138T630-576q-39 18-87 58.5T480-455Zm118 343q-2-61-18.5-138.5T528-384q43-66 127.5-114T834-557q10-2 18 1.5t14 9.5q6 6 9.5 14t2.5 18q-8 161-87.5 261.5T598-112Z"})}),(0,u.jsx)("span",{children:"Tools"})]})}),(0,u.jsx)("button",{onClick:()=>n("updates"),className:`group relative px-3 py-2 text-xs font-medium\n                    rounded-lg overflow-hidden focus:outline-none ${"updates"===t?"bg-bg-surface-hover text-span-default shadow-sm border border-border-l1 pointer-events-none":"hover:text-span-hover text-span-muted hover:bg-bg-hover border border-transparent"}\n                    transition-all duration-300`,children:(0,u.jsxs)("div",{className:"relative flex items-center gap-2",children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5 transform group-hover:rotate-180 transition-transform duration-500 fill-current",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,u.jsx)("span",{children:"Updates"}),l>0&&(0,u.jsx)("div",{className:"absolute -top-2 -right-2 bg-red-500 text-white text-[10px] font-bold rounded-full min-w-[16px] h-4 flex items-center justify-center px-1 shadow-lg",children:l>9?"9+":l})]})})]}),(0,u.jsx)("div",{className:"flex items-center gap-3 text-xs font-medium text-span-muted",children:(0,u.jsxs)("div",{className:"flex items-center gap-2",children:[(0,u.jsxs)("svg",{id:"status-indicator",className:"w-4 h-4",viewBox:"0 0 24 24",children:[(0,u.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"currentColor",className:"text-primary"}),(0,u.jsx)("polyline",{points:"17 9 10 16 7 13",fill:"none",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,u.jsx)("span",{id:"status-message",children:a})]})})]})})]})]})};const f=new class{constructor(){this.basePath=null,this.extensionsFilePath=null}async init(){try{const e=await c.getUserProfilePath();this.basePath=`${e}/.toolbox`,this.extensionsFilePath=`${this.basePath}/extensions.json`;await c.exists(this.basePath)||(console.info(`[StorageService] Creating base path: ${this.basePath}`),await c.createDirectory(this.basePath));await c.exists(this.extensionsFilePath)||(console.info(`[StorageService] Creating extensions.json file: ${this.extensionsFilePath}`),await c.writeFile(this.extensionsFilePath,JSON.stringify({extensions:[]},null,2))),console.info("[StorageService] Initialization complete")}catch(e){throw console.error("[StorageService] Initialization failed:",e.message),e}}getToolPath(e){return`${this.basePath}/${e}`}async readExtensionsFile(){try{const e=await c.readFile(this.extensionsFilePath);return JSON.parse(e)}catch(e){throw console.error("[StorageService] Failed to read extensions.json:",e.message),e}}async writeExtensionsFile(e){try{const t=JSON.stringify(e,null,2);await c.writeFile(this.extensionsFilePath,t),console.info("[StorageService] Successfully updated extensions.json")}catch(t){throw console.error("[StorageService] Failed to write to extensions.json:",t.message),t}}},p="2025.1.3",m=!1,h="https://msp.cbkdz.eu/api",g="/toolbox",v="/toolbox/report";let b={data:null,timestamp:0},y=null;const w=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return await async function(){if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])&&b.data&&Date.now()-b.timestamp<6e4)return console.debug("[API] Using cached data"),b.data;console.info("[API] Fetching fresh data");try{const e=`https://corsproxy.io/?url=${encodeURIComponent(`${h}${g}`)}`,t=await fetch(e,{method:"GET",headers:{Accept:"application/json"},mode:"cors",cache:"no-cache"});if(!t.ok)throw new Error(`Request failed with status ${t.status}`);const n=await t.json();return b={data:n,timestamp:Date.now()},n}catch(a){var e,t,n,r,o;console.error("[API] Error fetching data:",a.message);let l="Unable to connect to server";throw null!==(e=a.message)&&void 0!==e&&e.includes("Failed to fetch")||null!==(t=a.message)&&void 0!==t&&t.includes("network")?l="Network connection failed. Please check your internet connection.":null!==(n=a.message)&&void 0!==n&&n.includes("timeout")?l="Server request timed out. Please try again.":null!==(r=a.message)&&void 0!==r&&r.includes("500")?l="Server is temporarily unavailable. Please try again later.":null!==(o=a.message)&&void 0!==o&&o.includes("404")&&(l="Service endpoint not found. Please check for updates."),new Error(l)}}(e)}catch(t){throw console.error("[API] Error getting toolbox data:",t.message),t}},x=async()=>{try{if(y)return y;if(!c)throw console.error("[API] neuService is not available for OS detection."),new Error("neuService not available");const e=await c.getOSInfo();return y=e.name,console.info(`[API] Detected OS: ${y}`),y}catch(e){console.error("[API] Error getting OS info:",e.message);const t=navigator.userAgent.includes("Mac")?"mac":"windows";return console.warn(`[API] Falling back to browser detection: ${t}`),t}},k=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{var t,n,r;const o=await w(e),a=await x();return{currentVersion:p,latestVersion:(null===o||void 0===o||null===(t=o[a])||void 0===t?void 0:t.version)||p,downloadUrl:(null===o||void 0===o||null===(n=o[a])||void 0===n?void 0:n.downloadUrl)||null,changelog:(null===o||void 0===o||null===(r=o[a])||void 0===r?void 0:r.changelog)||[]}}catch(o){return console.warn("[API] Version info unavailable:",o.message),{currentVersion:p,latestVersion:p,downloadUrl:null,changelog:[]}}},S=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{const t=await w(e),n=await x();return null!==t&&void 0!==t&&t.tools&&Array.isArray(t.tools)?t.tools.map((e=>{const t=(null===e||void 0===e?void 0:e.name)||"unknown",r=t.toLowerCase().replace(/[^a-z0-9]/g,""),o=null===e||void 0===e?void 0:e[n];return{id:(null===e||void 0===e?void 0:e.id)||r,name:t,description:(null===e||void 0===e?void 0:e.description)||"",version:(null===o||void 0===o?void 0:o.version)||"1.0.0",downloadUrl:(null===o||void 0===o?void 0:o.downloadUrl)||null,icon:(null===e||void 0===e?void 0:e.icon)||"lightning-bolt",changelog:(null===o||void 0===o?void 0:o.changelog)||[],price:(null===e||void 0===e?void 0:e.price)||"0\u20ac"}})):(console.warn("[API] No tools data available or invalid format"),[])}catch(t){return console.error("[API] Error getting tools:",t.message),[]}},N=w,E=x,j=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{var t,n,r,o,a,l;const i=await w(e),s=await x();return{enabled:(null===i||void 0===i||null===(t=i[s])||void 0===t||null===(n=t.maintenance)||void 0===n?void 0:n.enabled)||!1,message:(null===i||void 0===i||null===(r=i[s])||void 0===r||null===(o=r.maintenance)||void 0===o?void 0:o.message)||"System is under maintenance",estimatedDuration:(null===i||void 0===i||null===(a=i[s])||void 0===a||null===(l=a.maintenance)||void 0===l?void 0:l.estimatedDuration)||"Unknown"}}catch(i){return console.warn("[API] Maintenance status unavailable:",i.message),{enabled:!1,message:"Unable to retrieve maintenance status.",estimatedDuration:"Unknown"}}},C=k,P=S,_=async e=>{console.warn("[API] Reporting exception...");try{const n=await fetch(`${h}${v}`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)});if(n.ok)return console.info("[API] Exception reported successfully"),!0;{let r="";try{r=await n.text()}catch(t){r="Could not read error response"}return console.error(`[API] Failed to report exception. Status: ${n.status}, Response: ${r}`),console.warn("EXCEPTION REPORT (copy this if needed):"),console.warn("----- BEGIN EXCEPTION REPORT -----"),console.warn(e),console.warn("----- END EXCEPTION REPORT -----"),!1}}catch(n){return console.error("[API] Error sending exception report:",n.message),console.warn("EXCEPTION REPORT (copy this if needed):"),console.warn("----- BEGIN EXCEPTION REPORT -----"),console.warn(e),console.warn("----- END EXCEPTION REPORT -----"),!1}},T=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{const[t,n]=await Promise.all([k(e),S(e)]);return{toolbox:{name:"Toolbox",version:t.latestVersion,changelog:t.changelog||[]},tools:n.map((e=>({name:e.name,version:e.version,changelog:e.changelog||[]}))).filter((e=>e.changelog.length>0))}}catch(t){return console.error("[API] Error getting comprehensive changelog:",t.message),{toolbox:{name:"Toolbox",version:"1.0.0",changelog:[]},tools:[]}}};const $=new class{async downloadFile(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;console.info(`[DownloadService] Starting download-first strategy: ${e} to ${t}`);try{const r=t.lastIndexOf("/");if(r>0){const e=t.substring(0,r);try{await c.createDirectory(e)}catch(p){throw console.error(`[DownloadService] Failed to create directory: ${e}`,p),new Error("Cannot create download directory. Please check permissions.")}}console.info(`[DownloadService] Fetching stream from: ${e}`),"function"===typeof n&&n({progress:0,phase:"Starting download...",loaded:0,total:0});try{await c.remove(t)}catch(m){}const o=await Promise.race([fetch(e,{method:"GET",mode:"cors",cache:"no-cache"}),new Promise(((e,t)=>setTimeout((()=>t(new Error("Download timed out"))),6e4)))]);if(!o.ok)throw new Error(`HTTP error: ${o.status} ${o.statusText}`);const a=o.body.getReader(),l=parseInt(o.headers.get("Content-Length"))||0;console.info(`[DownloadService] Downloading ${l?`${(l/1048576).toFixed(2)} MB`:"unknown size"} to memory...`);const i=[];let s=0,u=Date.now(),d=0,f=0;for(;;){f++;try{const{done:e,value:t}=await Promise.race([a.read(),new Promise(((e,t)=>setTimeout((()=>t(new Error("Read operation timed out"))),6e4)))]);if(e){console.info(`[DownloadService] Download complete after ${f} read operations`);break}if(t&&t.length>0&&(i.push(t),s+=t.length,u=Date.now(),d=0,l&&"function"===typeof n&&f%50===0)){n({progress:Math.round(s/l*90),phase:"Downloading to memory",loaded:s,total:l,size:`${(s/1048576).toFixed(2)} MB / ${(l/1048576).toFixed(2)} MB`})}const r=Date.now()-u;if(r>3e4)throw new Error(`Download stalled - no progress for ${Math.round(r/1e3)}s`)}catch(h){if(d++,h.message.includes("timed out")||h.message.includes("stalled"))throw h;if(d>=10)throw new Error(`Download failed after ${d} consecutive read failures: ${h.message}`);console.warn(`[DownloadService] Read failure ${d}/10, retrying:`,h.message);const e=100*d;await new Promise((t=>setTimeout(t,e)))}}console.info(`[DownloadService] Combining ${i.length} chunks into ArrayBuffer...`);const g=new Uint8Array(s);let v=0;for(const e of i)g.set(e,v),v+=e.length;const b=g.buffer.slice(g.byteOffset,g.byteOffset+g.byteLength);console.info("[DownloadService] Starting parallel write operations..."),await this.writeArrayBufferInParallel(t,b,n);if(!await c.exists(t))throw new Error("File download failed - file was not created");const y=await c.getStats(t);return console.info(`[DownloadService] Streaming download complete: ${t}, size: ${y.size} bytes`),"function"===typeof n&&n({progress:100,phase:"Download complete",loaded:s,total:l||s,size:`${(s/1048576).toFixed(2)} MB`}),!0}catch(m){var r,o,a,l,i,s,u,d,f;console.error("[DownloadService] Download failed:",m);try{await c.remove(t)}catch(g){console.warn(`[DownloadService] Could not cleanup partial file: ${g.message}`)}let e="Download failed";throw null!==(r=m.message)&&void 0!==r&&r.includes("timeout")||null!==(o=m.message)&&void 0!==o&&o.includes("timed out")?e="Download timed out. Please check your internet connection.":null!==(a=m.message)&&void 0!==a&&a.includes("HTTP error: 404")?e="File not found. This tool may no longer be available.":null!==(l=m.message)&&void 0!==l&&l.includes("HTTP error: 403")?e="Access denied. You may not have permission to download this file.":null!==(i=m.message)&&void 0!==i&&i.includes("network")||null!==(s=m.message)&&void 0!==s&&s.includes("fetch")?e="Network error. Please check your internet connection.":null!==(u=m.message)&&void 0!==u&&u.includes("space")||null!==(d=m.message)&&void 0!==d&&d.includes("ENOSPC")?e="Not enough disk space. Please free up some space and try again.":null!==(f=m.message)&&void 0!==f&&f.includes("Maximum read attempts")&&(e="Download stalled. Please try again."),"function"===typeof n&&n({progress:0,phase:e,error:e,resetState:!0}),new Error(e)}}async writeArrayBufferInParallel(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const r=2097152;console.info(`[DownloadService] Writing ${(t.byteLength/1048576).toFixed(2)} MB using parallel operations...`);const o=new ArrayBuffer(0);await c.writeBinaryFileArrayBuffer(e,o);const a=Math.ceil(t.byteLength/r),l=[];for(let s=0;s<a;s++){const e=s*r,n=Math.min(e+r,t.byteLength),o=t.slice(e,n);l.push({index:s,buffer:o,size:n-e})}console.info(`[DownloadService] Split into ${a} chunks for parallel writing`);let i=0;for(let s=0;s<l.length;s+=4){const o=Math.min(s+4,l.length),u=l.slice(s,o);console.info(`[DownloadService] Processing batch ${Math.floor(s/4)+1}/${Math.ceil(l.length/4)} (${u.length} chunks)`);const d=u.map((async o=>{if(await c.appendBinaryFileArrayBuffer(e,o.buffer),i++,"function"===typeof n){const e=Math.round(i/a*10);n({progress:90+e,phase:"Writing file to disk",loaded:i*r,total:t.byteLength,size:`${(t.byteLength/1048576).toFixed(2)} MB`})}console.debug(`[DownloadService] Completed chunk ${o.index+1}/${a} (${(o.size/1048576).toFixed(2)} MB)`)}));await Promise.all(d)}console.info("[DownloadService] Parallel write operations completed successfully")}};const L=new class{async cleanupDirectory(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{console.info(`[UtilityService] Cleaning up directory: ${e}`),t&&t({progress:0,phase:"Cleaning up directory..."});if(!await c.exists(e))return console.debug(`[UtilityService] Directory does not exist, creating: ${e}`),void await c.createDirectory(e);try{const r=await c.readDirectory(e);console.debug(`[UtilityService] Found ${r.length} items to process in directory`);const o=[],a=[];for(const t of r){const n=`${e}/${t}`;await this.isFile(n)&&t.toLowerCase().endsWith(".lic")?(o.push(t),console.info(`[UtilityService] Preserving license file: ${t}`)):a.push(t)}t&&t({progress:25,phase:`Preserving ${o.length} license files...`});for(let l=0;l<a.length;l++){const r=a[l],o=`${e}/${r}`;try{await c.remove(o),console.debug(`[UtilityService] Deleted: ${r}`)}catch(n){console.warn(`[UtilityService] Failed to delete ${r}: ${n.message}`)}if(t){t({progress:25+Math.floor((l+1)/a.length*75),phase:`Cleaning files... (${l+1}/${a.length})`})}}console.info(`[UtilityService] Directory cleaned selectively: ${e} (preserved ${o.length} license files)`)}catch(r){console.error(`[UtilityService] Error during selective cleanup, falling back to recreation: ${r.message}`);try{await c.remove(e),await c.createDirectory(e),console.info(`[UtilityService] Directory cleaned by recreation (fallback): ${e}`)}catch(o){throw console.error(`[UtilityService] Fallback cleanup also failed: ${o.message}`),o}}t&&t({progress:100,phase:"Directory cleanup completed"})}catch(r){throw console.error(`[UtilityService] Error during directory cleanup: ${r.message}`),r}}async extractZipFile(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{console.info(`[UtilityService] Extracting zip file: ${e} to ${t}`),n&&n({progress:0,phase:"Starting extraction..."});const r=e.replace(/\\/g,"/"),o=t.replace(/\\/g,"/"),a=`tar -xf "${r}" -C "${o}"`;console.debug(`[UtilityService] Running extraction command: ${a}`);const l=await c.execCommand(a);if(0!==l.exitCode){console.error(`[UtilityService] Tar extraction failed with exit code ${l.exitCode}`),console.error(`[UtilityService] Stderr: ${l.stdErr}`),console.info("[UtilityService] Falling back to platform-specific extraction");let e;e="windows"===(await c.getOSInfo()).name?`powershell -command "Expand-Archive -Path '${r}' -DestinationPath '${o}' -Force"`:`unzip -o '${r}' -d '${o}'`,console.debug(`[UtilityService] Running fallback extraction command: ${e}`);const t=await c.execCommand(e);if(0!==t.exitCode)throw new Error(`Extraction failed with code ${t.exitCode}: ${t.stdErr}`)}console.info(`[UtilityService] Extraction complete: ${e}`),await c.remove(e),n&&n({progress:100,phase:"Extraction complete"})}catch(d){var r,o,a,l,i,s,u;console.error(`[UtilityService] Extraction failed: ${d.message}`);let e="Failed to extract files";throw null!==(r=d.message)&&void 0!==r&&r.includes("Extraction failed with code")?e="File extraction failed. The download may be corrupted.":null!==(o=d.message)&&void 0!==o&&o.includes("permission")||null!==(a=d.message)&&void 0!==a&&a.includes("access")?e="Permission denied during extraction. Try running as administrator.":null!==(l=d.message)&&void 0!==l&&l.includes("space")||null!==(i=d.message)&&void 0!==i&&i.includes("ENOSPC")?e="Not enough disk space for extraction.":(null!==(s=d.message)&&void 0!==s&&s.includes("format")||null!==(u=d.message)&&void 0!==u&&u.includes("corrupt"))&&(e="Invalid or corrupted archive file."),n&&n({progress:0,phase:e,error:e,resetState:!0}),new Error(e)}}async isFile(e){try{return(await c.getStats(e)).isFile}catch(t){return console.warn(`[UtilityService] Error checking if ${e} is a file: ${t.message}`),!1}}async listDirectoryContents(e){try{return await c.readDirectory(e)}catch(t){return console.error(`[UtilityService] Error listing directory ${e}:`,t),[]}}arrayBufferToBase64(e){let t="";const n=new Uint8Array(e),r=n.byteLength;for(let o=0;o<r;o+=1e4){const e=n.slice(o,Math.min(o+1e4,r));t+=String.fromCharCode.apply(null,e)}return btoa(t)}async delay(e){return new Promise((t=>setTimeout(t,e)))}compareVersions(e,t){if(!e)return t?-1:0;if(!t)return 1;const n=e.split(".").map(Number),r=t.split(".").map(Number);for(let o=0;o<Math.max(n.length,r.length);o++){const e=n[o]||0,t=r[o]||0;if(e!==t)return e-t}return 0}};const z=new class{async installTool(e,t){const n=(null===e||void 0===e?void 0:e.name)||"Unknown Tool",r=(null===e||void 0===e?void 0:e.id)||(null===e||void 0===e?void 0:e.name)||"unknown";let o="initialization";try{console.info(`[ToolService] Installing tool: ${n}`);const a=f.getToolPath(r),l=`${a}/${r}.zip`;o="cleanup",t&&t({progress:5,phase:"Cleaning up directory..."}),await L.cleanupDirectory(a,(e=>{t&&t({progress:5+Math.floor(.05*e.progress),phase:e.phase})})),o="download",t&&t({progress:10,phase:"Downloading tool..."}),await $.downloadFile(e.downloadUrl,l,(e=>{t&&t({progress:10+Math.floor(.7*e.progress),phase:e.phase,loaded:e.loaded,total:e.total})}),!1),o="extraction",t&&t({progress:85,phase:"Extracting files..."}),await L.extractZipFile(l,a,(e=>{t&&t({progress:85+Math.floor(.1*e.progress),phase:e.phase})})),o="registration",t&&t({progress:95,phase:"Validating tool executable..."}),await this.addTool(r,n,e.version),t&&t({progress:100,phase:"Installation complete!"}),console.info(`[ToolService] Tool installed successfully: ${n}`)}catch(a){console.error(`[ToolService] Failed to install tool ${n} during ${o}:`,a.message),await this._resetToolState(r,`Installation failed during ${o}`);const e=this._getUserFriendlyErrorMessage(a,o);throw t&&t({progress:0,phase:e,error:e,resetState:!0}),new Error(e)}}async needsUpdate(e,t){try{const n=await this.getInstalledToolVersion(e);return!!n&&n!==t}catch(n){return console.error(`[ToolService] Failed to check for updates for ${e}:`,n.message),!1}}async uninstallTool(e){try{console.info(`[ToolService] Uninstalling tool: ${e}`),await this.removeTool(e),console.info(`[ToolService] Tool uninstalled successfully: ${e}`)}catch(t){console.error(`[ToolService] Failed to uninstall tool ${e}:`,t.message),await this._resetToolState(e,"Uninstall encountered issues");const n=this._getUserFriendlyErrorMessage(t,"uninstall");throw new Error(n)}}async addTool(e,t,n){try{const r=f.getToolPath(e),o=`${r}/${e}.exe`;await c.exists(r)||(console.info(`[ToolService] Creating directory for tool: ${r}`),await c.createDirectory(r));if(!await c.exists(o)){const e=`Tool executable not found: ${o}. Tool will not be registered to prevent runtime errors.`;throw console.warn(`[ToolService] ${e}`),new Error(e)}console.info(`[ToolService] Tool executable validated: ${o}`);const a=await f.readExtensionsFile(),l=a.extensions.findIndex((n=>{var r,o;return(null===(r=n.id)||void 0===r?void 0:r.toLowerCase())===(null===e||void 0===e?void 0:e.toLowerCase())||(null===(o=n.name)||void 0===o?void 0:o.toLowerCase())===(null===t||void 0===t?void 0:t.toLowerCase())}));l>=0?(a.extensions[l].version=n,console.info(`[ToolService] Updated existing tool ${t} to version ${n}`)):(a.extensions.push({id:e,name:t,version:n}),console.info(`[ToolService] Added new tool ${t} version ${n} to extensions`)),await f.writeExtensionsFile(a)}catch(r){throw console.error(`[ToolService] Failed to add tool ${t}:`,r.message),r}}async removeTool(e){try{let t=await f.readExtensionsFile();t?t.extensions||(t.extensions=[]):t={extensions:[]},t.extensions=t.extensions.filter((t=>{var n,r;return(null===(n=t.id)||void 0===n?void 0:n.toLowerCase())!==(null===e||void 0===e?void 0:e.toLowerCase())&&(null===(r=t.name)||void 0===r?void 0:r.toLowerCase())!==(null===e||void 0===e?void 0:e.toLowerCase())})),await f.writeExtensionsFile(t);const n=f.getToolPath(e);await c.exists(n)&&(console.info(`[ToolService] Removing directory for tool: ${n}`),await c.remove(n))}catch(t){throw console.error(`[ToolService] Failed to remove tool ${e}:`,t.message),t}}async isToolInstalled(e){try{return(await f.readExtensionsFile()).extensions.some((t=>{var n,r;return(null===(n=t.id)||void 0===n?void 0:n.toLowerCase())===(null===e||void 0===e?void 0:e.toLowerCase())||(null===(r=t.name)||void 0===r?void 0:r.toLowerCase())===(null===e||void 0===e?void 0:e.toLowerCase())}))}catch(t){throw console.error(`[ToolService] Failed to check if tool is installed: ${e}`,t.message),t}}async getTools(){try{return(await f.readExtensionsFile()).extensions}catch(e){throw console.error("[ToolService] Failed to retrieve tools:",e.message),e}}async getInstalledToolVersion(e){try{const t=(await this.getTools()).find((t=>{var n,r;return(null===(n=t.id)||void 0===n?void 0:n.toLowerCase())===(null===e||void 0===e?void 0:e.toLowerCase())||(null===(r=t.name)||void 0===r?void 0:r.toLowerCase())===(null===e||void 0===e?void 0:e.toLowerCase())}));return t?t.version:null}catch(t){throw console.error(`[ToolService] Failed to get installed version for tool ${e}:`,t.message),t}}async isToolRunning(e){try{console.debug(`[ToolService] Checking if tool is running: ${e}`);try{const t=await c.findProcessesByName(`${e}.exe`);if(t.length>0)return console.debug(`[ToolService] Found ${t.length} running processes for ${e}`),t[0].pid&&this.storeToolProcessId(e,t[0].pid),!0}catch(t){console.warn(`[ToolService] Error finding processes by name: ${t.message}`)}try{const t=localStorage.getItem("runningProcesses");if(t){const n=JSON.parse(t),r=n[e];if(r){if(await c.isProcessRunning(r))return!0;delete n[e],localStorage.setItem("runningProcesses",JSON.stringify(n))}}}catch(t){console.warn("[ToolService] Error accessing stored process info:",t.message)}return!1}catch(n){return console.error(`[ToolService] Failed to check if tool ${e} is running:`,n.message),!1}}async storeToolProcessId(e,t){try{if(!t)return void console.warn(`[ToolService] Attempted to store invalid PID for tool ${e}`);console.debug(`[ToolService] Storing process ID ${t} for tool ${e}`);try{const n=localStorage.getItem("runningProcesses"),r=n?JSON.parse(n):{};r[e]=t,localStorage.setItem("runningProcesses",JSON.stringify(r))}catch(n){console.warn("[ToolService] Could not store process ID in localStorage:",n.message)}}catch(r){console.error(`[ToolService] Failed to store process ID for tool ${e}:`,r.message)}}async runTool(e){try{const t=`${f.getToolPath(e)}/${e}.exe`;if(await this.isToolRunning(e))return console.info(`[ToolService] Tool ${e} is already running`),{success:!0,alreadyRunning:!0};if(!await c.exists(t))throw new Error(`Tool executable not found: ${t}`);console.info(`[ToolService] Starting tool: ${e}`);const n=await c.runExecutable(t);if(n.success){if(n.pid)return console.info(`[ToolService] Tool process spawned successfully: ${e} with PID ${n.pid}`),await this.storeToolProcessId(e,n.pid),{success:!0,pid:n.pid};console.warn(`[ToolService] Tool ${e} started but PID could not be determined. Will perform runtime checks.`);return await this.isToolRunning(e)?(console.info(`[ToolService] Found ${e} running after additional runtime check.`),{success:!0,delayed:!0}):{success:!0,warning:"PID not found but tool may still be starting"}}throw new Error(`Failed to start tool ${e}.`)}catch(t){console.error(`[ToolService] Failed to run tool ${e}:`,t.message),await this._resetToolState(e,"Failed to start tool");const n=this._getUserFriendlyErrorMessage(t,"startup");throw new Error(n)}}async _resetToolState(e,t){try{console.warn(`[ToolService] Resetting state for tool ${e}: ${t}`);try{localStorage.removeItem(`tool_pid_${e}`),localStorage.removeItem(`tool_running_${e}`)}catch(n){console.warn(`[ToolService] Could not clear localStorage for ${e}:`,n.message)}const o=f.getToolPath(e);if(await c.exists(o))try{const t=await c.readDirectory(o);(t.length<=2||t.some((e=>e.endsWith(".zip"))))&&(console.info(`[ToolService] Cleaning up incomplete installation for ${e}`),await L.cleanupDirectory(o))}catch(r){console.warn(`[ToolService] Could not cleanup directory for ${e}:`,r.message)}}catch(o){console.error(`[ToolService] Error during state reset for ${e}:`,o.message)}}_getUserFriendlyErrorMessage(e,t){var n;const r=(null===e||void 0===e||null===(n=e.message)||void 0===n?void 0:n.toLowerCase())||"";return r.includes("fetch")||r.includes("network")||r.includes("timeout")?"Connection problem. Please check your internet connection and try again.":"download"===t||r.includes("download")?r.includes("404")||r.includes("not found")?"Tool download file not found. This tool may no longer be available.":r.includes("403")||r.includes("unauthorized")?"Access denied. You may not have permission to download this tool.":"Download failed. Please check your internet connection and try again.":"extraction"===t||r.includes("extract")||r.includes("zip")?"Failed to extract tool files. The download may be corrupted.":"registration"===t||r.includes("tool executable not found")?"Tool installation incomplete. The executable file was not found after extraction.":"startup"===t||r.includes("spawn")||r.includes("executable")?"Could not start the tool. It may not be compatible with your system.":"uninstall"===t||r.includes("remove")||r.includes("delete")?"Could not completely remove tool files. Some files may remain.":r.includes("permission")||r.includes("access")||r.includes("eacces")?"Permission denied. Try running as administrator or check file permissions.":r.includes("space")||r.includes("enospc")?"Not enough disk space. Please free up some space and try again.":r.includes("registry")||r.includes("system")?"System configuration error. The tool may not be properly registered.":"An unexpected error occurred. Please try again or contact support if the problem persists."}},O=e=>{let{tool:t,onToolChange:n,isInstalled:o=!1,hasUpdate:a=!1,hasActiveOperations:l,addOperation:i,removeOperation:s}=e;const[d,f]=(0,r.useState)(0),[p,m]=(0,r.useState)(!1),[h,g]=(0,r.useState)("0 MB"),[v,b]=(0,r.useState)("Preparing..."),[y,w]=(0,r.useState)(!1),[x,k]=(0,r.useState)(o),[S,N]=(0,r.useState)(a),[E,j]=(0,r.useState)(!t.downloadUrl),[C,P]=(0,r.useState)(!1),[_,T]=(0,r.useState)(!1),$=(0,r.useRef)(null),L=(0,r.useRef)(null),O=(0,r.useRef)(0),F=t.id||t.name,A=p?"installing":E?"comingSoon":x?S?"updateAvailable":_?"running":"installed":"ready",M=async()=>{try{const e=await z.isToolRunning(t.name);!e&&_&&(console.info(`Tool ${t.name} is no longer running.`),$.current&&(console.info(`Stopping monitoring for tool ${t.name} since process is no longer available.`),clearInterval($.current),$.current=null)),T(e)}catch(e){console.debug(`Tool ${F} status check failed (likely normal):`,e.message),O.current++,O.current>=5&&(console.warn(`Too many consecutive errors checking ${F}, stopping monitoring`),$.current&&(clearInterval($.current),$.current=null),T(!1),O.current=0)}},D=e=>e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(2)} KB`:`${(e/1048576).toFixed(2)} MB`;r.useEffect((()=>{if(k(o),N(a),j(!t.downloadUrl),O.current=0,o){const e=setTimeout((()=>{M().catch((e=>{console.debug(`Initial process check failed for ${t.name}:`,e.message)}))}),1e3);return()=>clearTimeout(e)}return()=>{$.current&&(clearInterval($.current),$.current=null),L.current&&(clearInterval(L.current),L.current=null)}}),[o,a,t.downloadUrl]),r.useEffect((()=>{const e=e=>{y&&!e.target.closest(".tool-menu")&&w(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[y]);return(0,u.jsxs)("div",{className:"group relative "+(E?"":"hover-enabled"),children:[(0,u.jsx)("div",{className:`absolute inset-0 ${S?"bg-warning/5":_?"bg-success/5":"bg-primary/5"} \n              rounded-lg opacity-0 ${E?"":"group-hover:opacity-100"} transition-opacity duration-500 blur-xl`}),(0,u.jsx)("div",{className:`tool-card relative backdrop-blur-sm rounded-lg border \n              ${S?"border-warning/20":_?"border-success/20":"border-border-l1"} \n              bg-bg-surface\n              ${E?"":S?"hover:border-warning/40 hover:shadow-sm hover:shadow-warning/5":_?"hover:border-success/40 hover:shadow-sm hover:shadow-success/5":"hover:border-primary/40 hover:shadow-sm hover:shadow-primary/5"} \n              shadow-sm transition-all duration-300 ease-in-out`,children:(0,u.jsxs)("div",{className:"p-3 flex items-center gap-3",children:[(0,u.jsx)("div",{className:`w-10 h-10 rounded-lg ${E?"bg-label-muted/80":S?"bg-warning":_?"bg-success":"bg-primary"} \n                      flex items-center justify-center text-white \n                      shadow-sm\n                      ${E?"":"group-hover:brightness-110"} group-hover:scale-105 \n                      transition-all duration-300 ease-out`,children:(0,u.jsx)("svg",{className:"tool-icon w-5 h-5 transform group-hover:scale-110 transition-transform duration-300 "+(_?"animate-pulse":""),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:(e=>{switch(e){case"star":return"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z";case"lightning-bolt":default:return"M13 10V3L4 14h7v7l9-11h-7z";case"chat":return"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z";case"home":return"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6";case"user":return"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z";case"users":return"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"}})(t.icon)})})}),(0,u.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,u.jsxs)("div",{className:"flex items-center justify-between mb-1.5",children:[(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,u.jsx)("h3",{className:"tool-name text-sm font-semibold font-jakarta text-span-default transition-colors duration-200",children:t.name}),S&&(0,u.jsx)("span",{className:"update-indicator text-[10px] font-medium text-warning bg-bg-surface px-1.5 py-0.5 rounded-full border border-warning/20",children:"Update"}),_&&(0,u.jsx)("span",{className:"running-indicator text-[10px] font-medium text-success bg-bg-surface px-1.5 py-0.5 rounded-full border border-success/20",children:"Running"}),t.price&&(0,u.jsxs)("span",{className:"price-badge flex items-center gap-1 text-[10px] font-semibold px-2 py-0.5 rounded-md border transition-colors duration-200 "+("0\u20ac"===t.price?"bg-bg-surface text-success border-success/20 hover:bg-success/5":"bg-bg-surface text-primary border-primary/20 hover:bg-primary/5"),children:["0\u20ac"===t.price?(0,u.jsx)("svg",{className:"w-2.5 h-2.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}):(0,u.jsx)("svg",{className:"w-2.5 h-2.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),(0,u.jsx)("span",{children:"0\u20ac"===t.price?"Free":t.price})]}),void 0!==t.sort&&(0,u.jsxs)("span",{className:"tool-sort text-xs text-span-muted",children:["#",t.sort]})]}),(0,u.jsx)("p",{className:"tool-description text-xs text-span-muted transition-colors duration-200 line-clamp-1 mb-1.5",children:t.description}),(0,u.jsxs)("div",{className:"tool-meta-info flex items-center gap-2 text-[10px]",children:[x&&!E&&(0,u.jsxs)("div",{className:"status-badge flex items-center gap-1.5 px-2 py-0.5 rounded-full text-[10px] font-medium\n                      "+(S?"bg-bg-surface text-warning border border-warning/20":_?"bg-bg-surface text-success border border-success/20":"bg-bg-surface text-primary border border-primary/20"),children:[(0,u.jsx)("div",{className:`w-1.5 h-1.5 rounded-full ${(()=>{switch(A){case"installing":return"bg-warning animate-pulse";case"comingSoon":case"installed":return"bg-primary";case"updateAvailable":return"bg-warning";case"running":return"bg-success animate-pulse";default:return"bg-span-muted"}})()}`}),(0,u.jsx)("span",{children:(()=>{switch(A){case"installing":return"Installing...";case"comingSoon":return"Coming Soon";case"updateAvailable":return"Update Available";case"running":return"Running";case"installed":return"Installed";default:return"Ready to Install"}})()}),(0,u.jsx)("span",{className:"ml-0.5 text-span-muted",children:"\u2022"}),(0,u.jsxs)("span",{className:"text-span-default font-medium",children:["v",t.version]}),S&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("span",{className:"text-warning",children:"\u2192"}),(0,u.jsxs)("span",{className:"text-warning",children:["v",t.latestVersion||(parseFloat(t.version)+.1).toFixed(1)]})]})]}),E&&(0,u.jsxs)("div",{className:"status-badge flex items-center gap-1.5 px-2 py-0.5 rounded-full bg-bg-surface text-span-muted border border-border-l1 text-[10px] font-semibold",children:[(0,u.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-span-muted"}),(0,u.jsx)("span",{children:"Coming Soon"}),(0,u.jsx)("span",{className:"ml-0.5 text-span-muted",children:"\u2022"}),(0,u.jsxs)("span",{className:"text-span-default font-medium",children:["v",t.version]})]}),!x&&!E&&(0,u.jsxs)("div",{className:"status-badge flex items-center gap-1.5 px-2 py-0.5 rounded-full bg-bg-surface text-primary border border-primary/20 text-[10px] font-medium",children:[(0,u.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-primary"}),(0,u.jsx)("span",{children:"Ready to Install"}),(0,u.jsx)("span",{className:"ml-0.5 text-span-muted",children:"\u2022"}),(0,u.jsxs)("span",{className:"text-span-default font-medium",children:["v",t.version]})]})]})]}),(0,u.jsxs)("div",{className:"flex items-center",children:[(0,u.jsxs)("button",{className:(()=>{const e="install-button pluto-button group relative flex items-center gap-1.5 px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 ease-out not-draggable";return p||C||l?`${e} bg-bg-surface text-span-muted border border-border-l1\n        shadow-sm cursor-not-allowed opacity-80`:E?`${e} bg-bg-surface text-span-muted border border-border-l1 \n        shadow-sm pointer-events-none opacity-60`:_?`${e} bg-bg-surface-hover text-success border border-border-l1 \n        shadow-sm cursor-not-allowed`:x?S?`${e} bg-bg-surface hover:bg-bg-surface-hover \n          text-warning border border-border-l1 shadow-sm`:`${e} bg-bg-surface hover:bg-bg-surface-hover \n        text-span-default border border-border-l1 shadow-sm`:`${e} bg-bg-surface hover:bg-bg-surface-hover \n      text-span-default border border-border-l1 shadow-sm`})(),onClick:()=>{if(!_&&!l)return x&&!S?(async()=>{if(!x||C||_)return;const e=`start-${F}`;i(e);try{if(P(!0),(await z.runTool(t.name)).success){let n=0;const r=30;let o=500;const a=2e3;L.current&&(clearInterval(L.current),L.current=null);const l=async()=>{n++;try{const u=await z.isToolRunning(t.name);if(u||n>=r)if(L.current&&(clearInterval(L.current),L.current=null),T(u),P(!1),s(e),u){console.info(`Tool ${t.name} confirmed running after ${n} checks. Starting monitoring.`),O.current=0;try{await c.minimize(),console.info("Toolbox window minimized as tool is now running")}catch(i){console.debug("Failed to minimize toolbox window:",i.message)}$.current&&clearInterval($.current),$.current=setInterval((()=>{M()}),5e3)}else console.warn(`Tool ${t.name} failed to start after ${r} checks.`);else o=Math.min(1.2*o,a),setTimeout(l,o)}catch(u){console.debug(`Startup polling error for ${t.name}:`,u.message),L.current&&(clearInterval(L.current),L.current=null),P(!1),s(e)}};setTimeout(l,o)}else P(!1),s(e)}catch(n){console.error(`Failed to start tool ${t.name}:`,n.message),P(!1),s(e)}})():(async()=>{if(E)return;if(p||x&&!S)return;const e=`install-${F}`;i(e),m(!0),b("Preparing..."),f(0),g("0 MB");try{await z.installTool({...t,id:F},(e=>{if(f(e.progress),b(e.phase),e.loadedMB&&e.totalMB){const t=e.speed?` \u2022 ${e.speed}`:"";g(`${e.loadedMB} MB / ${e.totalMB} MB${t}`)}else e.size?g(e.size):"number"===typeof e.loaded&&"number"===typeof e.total&&e.total>0&&g(`${D(e.loaded)} / ${D(e.total)}`)})),f(100),b("Installation Complete"),k(!0),N(!1),n(F)}catch(r){console.error("Installation failed:",r),b(`Installation Failed: ${r.message}`)}finally{setTimeout((()=>{m(!1),s(e)}),1500)}})()},disabled:p||C,children:["                  ",(0,u.jsx)("svg",{className:"button-icon w-3.5 h-3.5 "+(p||C?"animate-spin":"transform group-hover:rotate-12 transition-transform duration-200"),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{className:"button-icon-path",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:(()=>{switch(A){case"installing":case"updateAvailable":return"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15";case"comingSoon":return"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z";case"running":return"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z";case"installed":return C?"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15":"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z M21 12a9 9 0 11-18 0 9 9 0 0118 0z";default:return"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"}})()})}),(0,u.jsx)("span",{className:"button-text",children:(()=>{switch(A){case"installing":return x?"Updating...":"Installing...";case"comingSoon":return"Coming Soon";case"updateAvailable":return"Update";case"running":return"Running";case"installed":return C?"Starting...":"Start";default:return"Install"}})()})]}),(0,u.jsxs)("div",{className:"tool-menu relative ml-1.5",children:[(0,u.jsx)("button",{className:`tool-menu-button p-2 rounded-lg\n                          ${x&&!l?"text-span-muted hover:text-span-hover hover:bg-bg-hover active:bg-bg-surface-hover border border-transparent hover:border-border-l1":"opacity-60 cursor-not-allowed text-span-muted/60 border border-transparent bg-transparent"}\n                          transition-all duration-200 ease-out`,onClick:l?void 0:()=>{w((e=>!e))},disabled:!x||l,children:(0,u.jsxs)("svg",{className:"w-4 h-4 pointer-events-none",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,u.jsx)("circle",{cx:"12",cy:"6",r:"2"}),(0,u.jsx)("circle",{cx:"12",cy:"12",r:"2"}),(0,u.jsx)("circle",{cx:"12",cy:"18",r:"2"})]})}),y&&(0,u.jsx)("div",{className:"tool-menu-dropdown absolute right-0 top-full mt-1 w-48 bg-bg-surface rounded-lg border border-border-l1 shadow-sm transform opacity-100 scale-100 translate-y-0 transition-all duration-200 z-[9999]",children:(0,u.jsx)("div",{className:"p-1",children:(0,u.jsxs)("button",{className:`uninstall-button ${_||l?"":"shine-effect"} flex w-full items-center gap-2 px-3 py-2 text-xs\n                            ${_||l?"text-error/50 bg-transparent opacity-60 cursor-not-allowed":"text-error hover:bg-bg-hover"}\n                            rounded-lg transition-colors duration-150 overflow-hidden`,onClick:async()=>{if(w(!1),!x)return;if(_)return void console.warn("Cannot uninstall while tool is running");const e=`uninstall-${F}`;i(e);try{await z.uninstallTool(F),$.current&&(clearInterval($.current),$.current=null),T(!1),k(!1),n(F)}catch(t){console.error("Uninstallation failed:",t),b(`Uninstallation Failed: ${t.message}`)}finally{setTimeout((()=>{m(!1),s(e)}),1500)}},disabled:_||l,children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),(0,u.jsx)("span",{children:_?"Currently running":l?"Operation in progress":"Uninstall"})]})})})]})]})]}),p&&(0,u.jsxs)("div",{className:"install-progress mt-3",children:[(0,u.jsxs)("div",{className:"flex items-center gap-2 mb-1.5",children:[(0,u.jsx)("div",{className:"flex-grow h-2 bg-bg-base rounded-full overflow-hidden border border-border-l1 shadow-inner",children:(0,u.jsx)("div",{className:`progress-bar h-full ${S?"bg-warning":"bg-primary"} transition-all duration-300 ease-out`,style:{width:`${d}%`}})}),(0,u.jsxs)("span",{className:"progress-percentage text-xs font-medium text-span-default min-w-[40px] text-right",children:[d,"%"]})]}),(0,u.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,u.jsx)("div",{className:"flex items-center justify-between",children:(0,u.jsx)("span",{className:"progress-text text-xs font-medium text-span-muted",children:v})}),(0,u.jsx)("div",{className:"flex items-center justify-end",children:(0,u.jsx)("span",{className:"progress-size text-xs font-medium text-span-muted",children:h})})]})]})]})]})})]})},F=e=>{let{isVisible:t,tools:n=[],isLoading:o,hasActiveOperations:a,addOperation:l,removeOperation:i}=e;const[s,c]=(0,r.useState)(!1),[d,f]=(0,r.useState)([]),[p,m]=(0,r.useState)(!1),h=async()=>{try{const e=(await Promise.all(n.map((async e=>{const t=e.id||e.name,n=await z.isToolInstalled(t),r=n?await z.getInstalledToolVersion(t):null,o=n&&e.version&&r!==e.version;return{...e,isInstalled:n,hasUpdate:o}})))).sort(((e,t)=>e.isInstalled===t.isInstalled?0:e.isInstalled?-1:1));f(e),c(0===e.length)}catch(e){console.error("Error checking tool installation status:",e),f(n)}finally{m(!1)}},g=async e=>{m(!0),await h()};(0,r.useEffect)((()=>{m(!0),c(0===n.length),h()}),[n]);const v=t?{}:{display:"none"};return(0,u.jsx)("div",{id:"tools-content",className:"tab-content h-full relative",style:v,children:(0,u.jsx)("div",{className:"h-[calc(100vh-125px)] relative opacity-100",children:(0,u.jsx)("div",{className:"h-full overflow-y-auto px-6 pt-6 pb-12 custom-scrollbar tools-fade-mask",id:"tools-scroll-content",children:o||p?(0,u.jsx)("div",{className:"flex flex-col items-center justify-center w-full",children:(0,u.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 w-full",children:[...Array(3)].map(((e,t)=>(0,u.jsxs)("div",{className:"group relative",children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-bg-surface/10 rounded-lg opacity-0 transition-opacity duration-500 blur-2xl"}),(0,u.jsx)("div",{className:"tool-card relative backdrop-blur-xl rounded-lg border bg-bg-surface border-border-l1 shadow-sm transition-all duration-300 ease-in-out animate-pulse",children:(0,u.jsxs)("div",{className:"p-3 flex items-center gap-3",children:[(0,u.jsx)("div",{className:"w-10 h-10 rounded-lg bg-bg-surface-hover flex items-center justify-center shadow-sm"}),(0,u.jsx)("div",{className:"flex-1 min-w-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between mb-1.5",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"flex items-center gap-1.5",children:(0,u.jsx)("div",{className:"h-4 bg-bg-surface-hover rounded w-24 mb-1"})}),(0,u.jsx)("div",{className:"h-2.5 bg-bg-surface-hover rounded w-full max-w-[180px] mb-1.5"}),(0,u.jsxs)("div",{className:"tool-status flex items-center gap-2 mt-1.5",children:[(0,u.jsxs)("div",{className:"flex items-center gap-1",children:[(0,u.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-bg-surface-hover"}),(0,u.jsx)("div",{className:"h-2 bg-bg-surface-hover rounded w-16"})]}),(0,u.jsx)("span",{className:"text-transparent",children:"\u2022"}),(0,u.jsx)("div",{className:"h-2 bg-bg-surface-hover rounded w-8"})]})]}),(0,u.jsxs)("div",{className:"flex items-center",children:[(0,u.jsx)("div",{className:"install-button h-[27px] w-20 bg-bg-surface-hover rounded-lg"}),(0,u.jsx)("div",{className:"w-8 h-8 ml-1.5 flex items-center justify-center",children:(0,u.jsx)("div",{className:"w-4 h-4 rounded-full bg-bg-surface-hover"})})]})]})})]})})]},`skeleton-${t}`)))})}):s?(0,u.jsxs)("div",{id:"no-tools-available",className:"flex flex-col items-center justify-center h-full py-12",children:[(0,u.jsxs)("div",{className:"relative w-20 h-20 mb-6",children:[(0,u.jsx)("div",{className:"absolute inset-0 rounded-full border-2 border-border-l1 animate-[spin_6s_linear_infinite]"}),(0,u.jsx)("div",{className:"absolute inset-[3px] rounded-full border-2 border-border-l1 animate-[spin_10s_linear_infinite_reverse]"}),(0,u.jsx)("div",{className:"absolute inset-[6px] rounded-full bg-bg-surface flex items-center justify-center",children:(0,u.jsx)("svg",{className:"w-10 h-10 text-span-muted",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})})]}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-span-default mb-2 font-jakarta",children:"No Tools Available"}),(0,u.jsx)("p",{className:"text-sm text-span-muted text-center max-w-md mb-6",children:"We couldn't fetch the available tools. This might be due to a connection issue or server maintenance."}),(0,u.jsxs)("button",{id:"reload-tools-button-empty",className:"flex items-center gap-2 px-5 py-2.5 rounded-lg \r bg-bg-surface hover:bg-bg-surface-hover \r text-span-default text-sm font-medium \r shadow-sm border border-border-l1 \r transition-all duration-200",onClick:()=>window.location.reload(),children:[(0,u.jsx)("svg",{className:"w-4 h-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,u.jsx)("span",{children:"Reload Tools"})]})]}):(0,u.jsx)("div",{id:"tools-container",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map((e=>{const t=e.id||e.name;return(0,u.jsx)(O,{tool:{...e,id:t,name:e.name},onToolChange:g,isInstalled:e.isInstalled,hasUpdate:e.hasUpdate,hasActiveOperations:a,addOperation:l,removeOperation:i},t)}))})})})})},A=e=>{let{isVisible:t,updateHistory:n=[],comprehensiveChangelog:o=null,isLoading:a}=e;const[l,i]=(0,r.useState)([]),[s,c]=(0,r.useState)("all"),d=(e,t)=>{if(!e||!t)return 0;const n=e=>e.toString().split(".").map((e=>parseInt(e)||0)),r=n(e),o=n(t),a=Math.max(r.length,o.length);for(let l=0;l<a;l++){const e=r[l]||0,t=o[l]||0;if(e>t)return 1;if(e<t)return-1}return 0},f=(0,r.useCallback)((()=>{if(!o)return n.map((e=>({...e,source:"toolbox",sourceName:"Toolbox",sourceVersion:"Unknown"})));const e=[];let t="windows";const r=navigator.userAgent.toLowerCase();r.includes("mac")?t="macos":r.includes("linux")&&(t="linux"),o.toolbox&&o.toolbox.changelog&&o.toolbox.changelog.forEach((t=>{e.push({...t,source:"toolbox",sourceName:"Toolbox",sourceVersion:o.toolbox.version})})),o.tools&&o.tools.forEach((n=>{if(n.changelog&&n.changelog.length>0){const r=n.name.toLowerCase();let o=!1;"windows"!==t||r.includes("macos")||r.includes("linux")?("macos"===t&&r.includes("macos")||"linux"===t&&r.includes("linux"))&&(o=!0):o=!0,o&&n.changelog.forEach((t=>{e.push({...t,source:"tool",sourceName:n.name,sourceVersion:n.version})}))}}));const a=[],l=new Set;return e.forEach((e=>{const t=`${e.source}-${e.sourceName}-${e.Version||e.version}`;l.has(t)||(l.add(t),a.push(e))})),a.sort(((e,t)=>{const n=new Date(e.ReleasedAt||e.releasedAt||0),r=new Date(t.ReleasedAt||t.releasedAt||0);if(n.getTime()!==r.getTime()&&n.getTime()>0&&r.getTime()>0)return r-n;const o=e.Version||e.version,a=t.Version||t.version;return d(a,o)})),a}),[n,o]);(0,r.useEffect)((()=>{const e=f();if(e.length>0){const t=[],n=new Map;e.forEach((e=>{const t=`${e.source}-${e.sourceName}`,r=e.Version||e.version;if(n.has(t)){const o=n.get(t);d(r,o.version)>0&&n.set(t,{version:r,entry:e})}else n.set(t,{version:r,entry:e})})),n.forEach((e=>{let{entry:n}=e;const r=`${n.source}-${n.sourceName}-${n.Version||n.version}`;t.push(r)})),i(t)}}),[n,o,f]);const p=()=>{const e=f();return"all"===s?e:"toolbox"===s?e.filter((e=>"toolbox"===e.source)):e.filter((e=>e.sourceName===s))};return t?(0,u.jsxs)("div",{id:"updates-content",className:"tab-content h-full flex flex-col",children:[(0,u.jsx)("div",{className:"flex-shrink-0 px-8 pt-6 pb-4 bg-bg-surface border-b border-border-l1",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{className:"text-sm font-medium text-span-default",children:"Update History"}),(0,u.jsx)("p",{className:"text-[11px] text-span-muted",children:"All updates across toolbox and tools"})]}),o&&(0,u.jsxs)("div",{className:"flex items-center gap-2",children:[(0,u.jsx)("span",{className:"text-xs text-span-muted",children:"Filter:"}),(0,u.jsx)("select",{value:s,onChange:e=>c(e.target.value),className:"px-3 py-1.5 text-xs bg-bg-surface-hover border border-border-l1 rounded-md text-span-default focus:outline-none min-w-[120px]",children:(()=>{const e=[{value:"all",label:"All Updates"}];return o&&(e.push({value:"toolbox",label:"Toolbox"}),o.tools&&o.tools.forEach((t=>{e.push({value:t.name,label:t.name})}))),e})().map((e=>(0,u.jsx)("option",{value:e.value,children:e.label},e.value)))})]})]})}),(0,u.jsx)("div",{className:"flex-1 overflow-y-auto px-8 py-6 custom-scrollbar",children:a?(0,u.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,u.jsxs)("div",{className:"relative w-20 h-20 mb-6",children:[(0,u.jsx)("div",{className:"absolute inset-0 rounded-full border-2 border-border-l1 animate-[spin_6s_linear_infinite]"}),(0,u.jsx)("div",{className:"absolute inset-[3px] rounded-full border-2 border-border-l1 animate-[spin_10s_linear_infinite_reverse]"}),(0,u.jsx)("div",{className:"absolute inset-[6px] rounded-full bg-bg-surface flex items-center justify-center",children:(0,u.jsx)("svg",{className:"w-10 h-10 text-span-muted",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeKJoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-span-default mb-2 font-jakarta",children:"Refreshing changelog history..."}),(0,u.jsx)("p",{className:"text-sm text-span-muted text-center max-w-md",children:"Please wait while we retrieve the latest updates."})]}):0===n.length?(0,u.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,u.jsxs)("div",{className:"relative w-20 h-20 mb-6",children:[(0,u.jsx)("div",{className:"absolute inset-0 rounded-full border-2 border-border-l1 animate-[spin_6s_linear_infinite]"}),(0,u.jsx)("div",{className:"absolute inset-[3px] rounded-full border-2 border-border-l1 animate-[spin_10s_linear_infinite_reverse]"}),(0,u.jsx)("div",{className:"absolute inset-[6px] rounded-full bg-bg-surface flex items-center justify-center",children:(0,u.jsx)("svg",{className:"w-10 h-10 text-span-muted",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})]}),(0,u.jsx)("h3",{className:"text-lg font-semibold text-span-default mb-2 font-jakarta",children:"No Update History Available"}),(0,u.jsx)("p",{className:"text-sm text-span-muted text-center max-w-md",children:"We couldn't retrieve the update history. This might be due to a connection issue or the data hasn't been provided by the server."})]}):(0,u.jsx)("div",{children:(0,u.jsx)("div",{className:"relative pl-8 space-y-8 before:absolute before:inset-y-0 before:left-3 before:w-px before:bg-gradient-to-b before:from-primary before:via-primary/50 before:to-border-l1",children:0===p().length?(0,u.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,u.jsx)("div",{className:"w-16 h-16 rounded-full bg-bg-surface-hover flex items-center justify-center mb-4",children:(0,u.jsx)("svg",{className:"w-8 h-8 text-span-muted",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,u.jsx)("h4",{className:"text-sm font-medium text-span-default mb-2",children:"No updates found"}),(0,u.jsx)("p",{className:"text-xs text-span-muted text-center max-w-sm",children:"No update history is available for the selected filter."})]}):p().map(((e,t)=>{const n=(e=>{const t=f(),n=`${e.source}-${e.sourceName}`;let r=null,o=null;return t.forEach((e=>{if(`${e.source}-${e.sourceName}`===n){const t=e.Version||e.version;(!r||d(t,r)>0)&&(r=t,o=`${e.source}-${e.sourceName}-${t}`)}})),`${e.source}-${e.sourceName}-${e.Version||e.version}`===o})(e),r=`${e.source}-${e.sourceName}-${e.Version||e.version||t}`,o=l.includes(r),a=(e=>{if(!e)return"";try{const t=new Date(e);return isNaN(t.getTime())?e:t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(t){return console.error(`[Updates] Error formatting date: ${t.message}`),e}})(e.ReleasedAt||e.releasedAt||e.date),s=e.New||e.new||[],c=e.Improvements||e.improvements||[],p=e.Fixes||e.fixes||[],m=Array.isArray(s)&&s.length>0,h=Array.isArray(c)&&c.length>0,g=Array.isArray(p)&&p.length>0;return(0,u.jsxs)("div",{className:"relative update-history-item group",children:[(0,u.jsx)("div",{className:`absolute -left-8 top-1 w-6 h-6 rounded-full\n                                ${n?"bg-bg-surface border-2 border-primary":"bg-bg-surface border-2 border-border-l2"}\n                                flex items-center justify-center z-10\n                                group-hover:scale-110\n                                ${n?"group-hover:border-primary group-hover:bg-bg-surface-hover":"group-hover:border-span-muted group-hover:bg-bg-surface-hover"}\n                                transition-all duration-300`,children:(0,u.jsx)("div",{className:`w-1.5 h-1.5 rounded-full\n                                  ${n?"bg-primary":"bg-span-muted"}\n                                  transition-colors duration-300`})}),(0,u.jsxs)("div",{className:"relative rounded-lg border border-border-l1 bg-bg-surface overflow-hidden\n                                group-hover:border-border-l2 group-hover:shadow-sm\n                                transition-all duration-300",children:[(0,u.jsx)("div",{className:"p-4 bg-bg-surface",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsx)("div",{className:"flex items-center gap-3",children:(0,u.jsxs)("div",{className:"flex items-center gap-1.5 px-2 py-1 rounded-md text-[10px] font-medium bg-primary/10 text-primary border border-primary/20",children:["toolbox"===e.source?(0,u.jsx)("svg",{className:"w-3 h-3",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1L5 3l4 2 4-2-4-2z"})}):(0,u.jsx)("svg",{className:"w-3 h-3",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})}),(0,u.jsx)("span",{children:e.sourceName}),(0,u.jsx)("div",{className:"w-1 h-1 rounded-full bg-primary/60"}),(0,u.jsx)("span",{children:e.Version||e.version||"Unknown"}),n&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"w-1 h-1 rounded-full bg-primary/60"}),(0,u.jsx)("span",{children:"Latest"})]})]})}),(0,u.jsx)("span",{className:"text-xs text-span-muted",children:a})]})}),(0,u.jsx)("div",{className:`px-4 pb-4 ${n||o||!(m||h||g)?"":"hidden"} changelog-details`,children:(0,u.jsxs)("div",{className:"space-y-3",children:[m&&(0,u.jsxs)("div",{className:"changelog-category",children:[(0,u.jsxs)("h5",{className:"text-xs font-medium text-primary mb-2 flex items-center gap-1.5",children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})}),(0,u.jsx)("span",{children:"New Features"})]}),(0,u.jsx)("ul",{className:"space-y-1 pl-5 list-disc text-xs text-span-muted",children:s.map(((e,t)=>(0,u.jsx)("li",{children:e},`feature-${t}`)))})]}),h&&(0,u.jsxs)("div",{className:"changelog-category",children:[(0,u.jsxs)("h5",{className:"text-xs font-medium text-success mb-2 flex items-center gap-1.5",children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),(0,u.jsx)("span",{children:"Improvements"})]}),(0,u.jsx)("ul",{className:"space-y-1 pl-5 list-disc text-xs text-span-muted",children:c.map(((e,t)=>(0,u.jsx)("li",{children:e},`improvement-${t}`)))})]}),g&&(0,u.jsxs)("div",{className:"changelog-category",children:[(0,u.jsxs)("h5",{className:"text-xs font-medium text-warning mb-2 flex items-center gap-1.5",children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,u.jsx)("span",{children:"Bug Fixes"})]}),(0,u.jsx)("ul",{className:"space-y-1 pl-5 list-disc text-xs text-span-muted",children:p.map(((e,t)=>(0,u.jsx)("li",{children:e},`fix-${t}`)))})]}),!m&&!h&&!g&&(0,u.jsx)("p",{className:"text-xs text-span-muted italic",children:"No detailed changelog available for this version."})]})}),!n&&(m||h||g)&&(0,u.jsxs)("button",{className:"changelog-toggle w-full p-2 text-xs text-span-default font-medium border-t border-border-l1 hover:bg-bg-surface-hover transition-colors flex items-center justify-center gap-2",onClick:()=>(e=>{l.includes(e)?i(l.filter((t=>t!==e))):i([...l,e])})(r),children:[(0,u.jsx)("span",{className:"show-text",children:o?"Hide Details":"Show Details"}),(0,u.jsx)("svg",{className:"w-3.5 h-3.5 changelog-toggle-icon transition-transform duration-300\n                                  "+(o?"rotate-180":""),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]})]})]},r)}))})})})]}):null},M=e=>{let{onRefreshTools:t,onRefreshUpdates:n,onCheckUpdates:o,hasActiveOperations:a}=e;const[l,i]=(0,r.useState)(!1),s=(0,r.useRef)(null),c=(0,r.useRef)(null);(0,r.useEffect)((()=>{const e=e=>{l&&s.current&&c.current&&!s.current.contains(e.target)&&!c.current.contains(e.target)&&i(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[l]);return(0,u.jsx)("div",{className:"flex-none px-6 py-2.5 bg-bg-base border-t border-border-l1 shadow-sm status-bar-container",children:(0,u.jsxs)("div",{className:"relative flex items-center justify-between",children:[(0,u.jsxs)("button",{id:"quick-actions-button",ref:c,className:`group relative flex items-center gap-2 px-4 py-1.5 rounded-lg\n                 ${a?"bg-bg-surface text-span-muted border border-border-l1 cursor-not-allowed opacity-60":"bg-bg-surface hover:bg-bg-surface-hover text-span-default border border-border-l1"}\n                 text-xs font-medium\n                 shadow-sm\n                 transition-all duration-200 ease-out\n                 not-draggable`,onClick:a?void 0:()=>{i(!l)},disabled:a,children:[(0,u.jsx)("svg",{className:"w-3.5 h-3.5 transform group-hover:rotate-12 transition-transform duration-200",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"})}),(0,u.jsx)("span",{children:"Quick Actions"}),(0,u.jsx)("svg",{className:"w-3.5 h-3.5 text-span-default transition-transform duration-300 "+(l?"rotate-180":""),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),(0,u.jsxs)("div",{ref:s,id:"quick-actions-dropdown",className:"absolute left-0 bottom-full mb-2 w-64 \n                bg-bg-surface rounded-lg border border-border-l1 shadow-lg \n                transform transition-all duration-200 z-[9999]\n                "+(l?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-95 -translate-y-2 pointer-events-none"),children:[(0,u.jsx)("div",{className:"p-3 bg-bg-surface border-b border-border-l1",children:(0,u.jsx)("h3",{className:"font-medium text-span-default text-sm",children:"Quick Actions"})}),(0,u.jsxs)("div",{className:"p-2 space-y-1",children:[(0,u.jsxs)("button",{id:"check-updates-button",className:"flex w-full items-center gap-3 px-3 py-2 text-sm\n                     rounded-lg transition-all duration-150 not-draggable\n                     "+(a?"text-span-muted cursor-not-allowed opacity-60":"text-span-default hover:bg-bg-surface-hover"),onClick:()=>{a||(i(!1),"function"===typeof o&&o(),"function"===typeof t&&t())},disabled:a,children:[(0,u.jsx)("svg",{className:"w-4 h-4 text-span-default",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,u.jsx)("span",{children:"Check for Updates"})]}),(0,u.jsxs)("button",{id:"reload-updates-button",className:"flex w-full items-center gap-3 px-3 py-2 text-sm\n                     rounded-lg transition-all duration-150 not-draggable\n                     "+(a?"text-span-muted cursor-not-allowed opacity-60":"text-span-default hover:bg-bg-surface-hover"),onClick:()=>{a||(i(!1),"function"===typeof n&&n())},disabled:a,children:[(0,u.jsx)("svg",{className:"w-4 h-4 text-span-default",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M19 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,u.jsx)("span",{children:"Reload Updates"})]}),(0,u.jsxs)("button",{id:"reload-tools-button",className:"flex w-full items-center gap-3 px-3 py-2 text-sm\n                     rounded-lg transition-all duration-150 not-draggable\n                     "+(a?"text-span-muted cursor-not-allowed opacity-60":"text-span-default hover:bg-bg-surface-hover"),onClick:()=>{a||(i(!1),"function"===typeof t&&t())},disabled:a,children:[(0,u.jsx)("svg",{className:"w-4 h-4 text-span-default",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,u.jsx)("span",{children:"Reload Tools"})]})]})]}),(0,u.jsx)("span",{className:"text-xs text-span-muted font-jakarta",children:"Made with \u2665 by Pluto"})]})})},D=e=>{let{onInitializationComplete:t}=e;const[n,o]=(0,r.useState)("Initializing..."),[a,l]=(0,r.useState)(0);return(0,r.useEffect)((()=>{const e=[{status:"Initializing...",progress:15},{status:"Loading data...",progress:35},{status:"Setting up...",progress:60},{status:"Almost ready...",progress:85},{status:"Ready",progress:100}];let n=!0;const r=e=>new Promise((t=>setTimeout(t,e)));return(async()=>{try{for(let t=0;t<e.length;t++){if(!n)return;const a=e[t];o(a.status),l(a.progress),await r(200)}n&&t()}catch(a){console.error("Error in initialization:",a),n&&t()}})(),()=>{n=!1}}),[t]),(0,u.jsxs)("div",{className:"popup-container bg-bg-base flex items-center justify-center min-h-screen w-full overflow-hidden",children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-bg-surface/30 backdrop-blur-[120px]"}),(0,u.jsx)("div",{className:"relative z-10 w-full sm:w-4/5 md:w-3/4 lg:w-2/3 xl:w-1/2 max-w-2xl mx-auto px-6",children:(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsxs)("div",{className:"mb-8",children:[(0,u.jsxs)("div",{className:"relative mx-auto w-16 h-16 mb-4",children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-bg-surface/10 rounded-full blur-lg"}),(0,u.jsx)("img",{src:"https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/logo.png",alt:"Pluto Logo",className:"relative h-14 w-16"})]}),(0,u.jsx)("h2",{className:"text-xl font-bold text-span-default tracking-wide font-jakarta",children:"Pluto Toolbox"}),(0,u.jsx)("p",{className:"text-sm text-span-muted",children:"MovieStarPlanet Extension"})]}),(0,u.jsx)("div",{className:"space-y-6 mb-8",children:(0,u.jsxs)("div",{className:"space-y-2 px-4",children:[(0,u.jsx)("div",{className:"h-2 w-full bg-bg-surface/50 rounded-full overflow-hidden border border-border-l1 shadow-inner",children:(0,u.jsx)("div",{className:"h-full bg-text-main rounded-full transition-all duration-500 ease-out",style:{width:`${a}%`}})}),(0,u.jsxs)("div",{className:"flex justify-between items-center",children:[(0,u.jsx)("p",{className:"text-base font-medium text-span-default",children:n}),(0,u.jsxs)("span",{className:"text-xs text-span-muted",children:[a,"%"]})]})]})})]})})]})},R=e=>{let{maintenanceInfo:t,onMaintenanceRefresh:n}=e;const[o,a]=(0,r.useState)(!1);return(0,u.jsxs)("div",{id:"maintenance-container",className:"bg-bg-surface rounded-lg shadow-sm overflow-hidden border border-border-l1",children:[(0,u.jsxs)("div",{className:"pt-5 px-5 text-center",children:[(0,u.jsx)("div",{className:"flex justify-center mb-6",children:(0,u.jsxs)("div",{className:"relative w-20 h-20",children:[(0,u.jsx)("div",{className:"absolute inset-0 rounded-full bg-bg-surface-hover flex items-center justify-center border border-border-l1",children:(0,u.jsx)("svg",{className:"w-10 h-10 text-primary",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v2m0 8v2m-6-6h2m8 0h2m-9.177-5.764l1.415 1.415m7.072 7.072l1.415 1.415M6.343 17.657l1.415-1.415m7.072-7.072l1.415-1.415"})})}),(0,u.jsx)("div",{className:"absolute inset-0 rounded-full border-2 border-primary/20 animate-[spin_6s_linear_infinite]"}),(0,u.jsx)("div",{className:"absolute inset-[3px] rounded-full border-2 border-primary/10 animate-[spin_10s_linear_infinite_reverse]"})]})}),(0,u.jsx)("h2",{className:"text-xl font-bold text-span-default mb-2 font-jakarta",children:"Maintenance in Progress"}),(0,u.jsx)("p",{id:"maintenance-message",className:"text-sm text-span-muted mb-4",children:t.message}),(0,u.jsx)("div",{className:"flex justify-center items-center gap-2 mb-6",children:(0,u.jsxs)("div",{className:"flex flex-col items-center",children:[(0,u.jsx)("span",{className:"text-xs font-medium px-2 py-0.5 rounded-full bg-bg-surface-hover text-primary mb-1 border border-primary/20",children:"Estimated Duration"}),(0,u.jsx)("span",{id:"estimated-duration",className:"text-sm font-semibold text-primary",children:t.estimatedDuration})]})}),(0,u.jsxs)("div",{className:"text-left bg-bg-surface-hover rounded-lg p-3 border border-border-l1 mb-6",children:[(0,u.jsx)("h3",{className:"text-xs font-semibold text-span-default mb-2",children:"What to expect"}),(0,u.jsxs)("ul",{className:"text-xs text-span-muted space-y-1 pl-4 list-disc",children:[(0,u.jsx)("li",{children:"System improvements"}),(0,u.jsx)("li",{children:"Performance optimization"}),(0,u.jsx)("li",{children:"New features being added"})]})]})]}),(0,u.jsx)("div",{id:"maintenance-button-container",className:"border-t border-border-l1",children:(0,u.jsxs)("button",{id:"refresh-maintenance-button",className:"flex-1 w-full py-4 text-sm font-medium text-primary hover:bg-bg-hover transition-colors flex items-center justify-center gap-2 "+(o?"checking":""),onClick:async()=>{a(!0),await n(),setTimeout((()=>a(!1)),1500)},disabled:o,children:[(0,u.jsx)("span",{id:"refresh-maintenance-button-text",children:"Check Again"}),(0,u.jsx)("svg",{className:"spinner-icon w-4 h-4 animate-spin transition-opacity duration-300 "+(o?"opacity-100":"opacity-0 hidden"),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})]})})]})};const I=new class{async checkForUpdates(){try{console.info("[UpdateService] Checking for updates...");const e=await C(!0),t=await c.checkForUpdates("https://dl.cbkdz.eu/toolbox/app/manifest.json");if(e.latestVersion!==p)return console.info(`[UpdateService] Update available: ${e.latestVersion} -> ${t.version}`),await this.install()}catch(e){return console.error("[UpdateService] Error checking for updates:",e.message),!1}return!1}async install(){try{var e=await c.install();return e.success&&await c.restart(),e.success}catch(t){return console.error("[UpdateService] Error during installation:",t.message),!1}}},U=e=>{let{updateInfo:t}=e;const[n,o]=(0,r.useState)(!1);return(0,u.jsxs)("div",{id:"update-container",className:"bg-bg-surface rounded-lg shadow-sm overflow-hidden max-w-[400px] border border-border-l1",children:[(0,u.jsxs)("div",{className:"pt-6 px-6 text-center",children:[(0,u.jsx)("div",{className:"flex justify-center mb-4",children:(0,u.jsxs)("div",{className:"relative w-12 h-12",children:[(0,u.jsx)("div",{className:"absolute inset-0 rounded-full bg-bg-surface-hover flex items-center justify-center border border-border-l1",children:(0,u.jsx)("svg",{className:"w-6 h-6 text-primary "+(n?"animate-spin":""),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,u.jsx)("div",{className:"absolute inset-0 rounded-full border-2 border-primary/20 animate-[spin_6s_linear_infinite]"}),(0,u.jsx)("div",{className:"absolute inset-[3px] rounded-full border-2 border-primary/10 animate-[spin_10s_linear_infinite_reverse]"})]})}),(0,u.jsx)("h2",{className:"text-lg font-semibold text-span-default mb-1 font-jakarta",children:"Update Available"}),(0,u.jsx)("p",{className:"text-xs text-span-muted mb-4",children:"New version available with improvements and features."}),(0,u.jsxs)("div",{className:"flex justify-center items-center gap-3 mb-4",children:[(0,u.jsxs)("div",{className:"flex items-center gap-1.5 px-2 py-1 rounded-md text-[10px] font-medium bg-bg-surface-hover text-span-muted border border-border-l1",children:[(0,u.jsx)("span",{children:"Current"}),(0,u.jsx)("div",{className:"w-1 h-1 rounded-full bg-span-muted/60"}),(0,u.jsx)("span",{id:"current-version",children:t.currentVersion})]}),(0,u.jsx)("svg",{className:"w-4 h-4 text-border-l1",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 5l7 7-7 7M5 5l7 7-7 7"})}),(0,u.jsxs)("div",{className:"flex items-center gap-1.5 px-2 py-1 rounded-md text-[10px] font-medium bg-primary/10 text-primary border border-primary/20",children:[(0,u.jsx)("span",{children:"New"}),(0,u.jsx)("div",{className:"w-1 h-1 rounded-full bg-primary/60"}),(0,u.jsx)("span",{id:"latest-version",children:t.latestVersion})]})]}),(0,u.jsxs)("div",{className:"text-left bg-bg-surface-hover rounded-md p-3 border border-border-l1 mb-4",children:[(0,u.jsx)("h3",{className:"text-[11px] font-medium text-span-default mb-1.5",children:"What's new"}),(0,u.jsx)("ul",{className:"text-[11px] text-span-muted space-y-0.5 pl-3 list-disc",children:(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("li",{children:"Performance improvements"}),(0,u.jsx)("li",{children:"New tools and features"}),(0,u.jsx)("li",{children:"Bug fixes and stability enhancements"})]})})]})]}),(0,u.jsx)("div",{id:"update-button-container",className:"border-t border-border-l1 "+(n?"hidden":"block"),children:(0,u.jsx)("button",{id:"install-update-button",className:"flex-1 w-full py-3 text-sm font-medium text-primary hover:bg-bg-hover transition-colors",onClick:async()=>{o(!0),await I.checkForUpdates()||o(!1)},disabled:n,children:"Update Now"})}),n&&(0,u.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,u.jsx)("p",{className:"text-sm text-primary font-medium",children:"Updating..."})})]})},B=e=>{let{onTriggerError:t}=e;const[n,o]=(0,r.useState)(!1),[a,l]=(0,r.useState)(m);return console.log("[DevModePanel] DEV_MODE:",m,"isVisible:",a),console.log("[DevModePanel] Component is rendering!"),r.useEffect((()=>{m}),[]),console.log("[DevModePanel] Not rendering due to DEV_MODE or visibility"),null},V=e=>{let{isVisible:t,errorMessage:n,onClose:o}=e;const a=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useEffect)((()=>{const e=e=>{"Escape"===e.key&&t&&o()};return t&&(document.addEventListener("keydown",e),setTimeout((()=>{var e;null===(e=l.current)||void 0===e||e.focus()}),100)),()=>{document.removeEventListener("keydown",e)}}),[t,o]),(0,r.useEffect)((()=>(document.body.style.overflow=t?"hidden":"unset",()=>{document.body.style.overflow="unset"})),[t]),t?(0,u.jsx)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm",onClick:o,children:(0,u.jsxs)("div",{ref:a,className:"relative bg-bg-surface border border-border-l1 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out",onClick:e=>e.stopPropagation(),children:[(0,u.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border-l1",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,u.jsx)("div",{className:"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center",children:(0,u.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{className:"text-lg font-semibold text-span-primary",children:"Oops! Something went wrong"}),(0,u.jsx)("p",{className:"text-sm text-span-muted",children:"An unexpected error occurred"})]})]}),(0,u.jsx)("button",{ref:l,onClick:o,className:"text-span-muted hover:text-span-primary transition-colors p-1 rounded-lg hover:bg-bg-hover","aria-label":"Close error dialog",children:(0,u.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),(0,u.jsxs)("div",{className:"p-6",children:[(0,u.jsx)("div",{className:"mb-4",children:(0,u.jsx)("p",{className:"text-span-secondary text-sm leading-relaxed",children:"We've encountered an unexpected error. Don't worry - this has been automatically reported to our development team and we'll work on fixing it."})}),(0,u.jsxs)("details",{className:"mb-4",children:[(0,u.jsx)("summary",{className:"text-xs text-span-muted cursor-pointer hover:text-span-secondary transition-colors select-none",children:"Technical details (click to expand)"}),(0,u.jsx)("div",{className:"mt-2 p-3 bg-bg-hover rounded-lg border border-border-l1",children:(0,u.jsx)("code",{className:"text-xs text-span-muted font-mono break-all",children:n||"An unexpected error occurred"})})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-span-muted mb-4",children:[(0,u.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,u.jsx)("span",{children:"Error automatically reported to developers"})]})]}),(0,u.jsx)("div",{className:"flex justify-end p-6 border-t border-border-l1",children:(0,u.jsx)("button",{onClick:o,className:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-bg-surface",children:"OK"})})]})}):null};const W=new class{constructor(){this.isInitialized=!1,this.maxStackLength=1500,this.errorCache=new Map,this.maxErrorsPerMinute=3,this.errorTimeWindow=6e4,this.duplicateErrorWindow=6e4,this.lastCleanup=Date.now(),this.cleanupInterval=3e4}init(){this.isInitialized?console.debug("[ErrorReportingService] Already initialized, skipping."):(this._setupErrorHandlers(),this.isInitialized=!0,console.info("[ErrorReportingService] Initialized with enhanced duplicate suppression."))}_shouldReportError(e){const t=Date.now();t-this.lastCleanup>this.cleanupInterval&&(this._cleanupErrorCache(t),this.lastCleanup=t);const n=this._createErrorKey(e);if(this.errorCache.has(n)){const e=this.errorCache.get(n);if(t-e<this.duplicateErrorWindow)return console.debug("[ErrorReportingService] Suppressing duplicate error:",n.substring(0,50)+"..."),!1}return Array.from(this.errorCache.values()).filter((e=>t-e<this.errorTimeWindow)).length>=this.maxErrorsPerMinute?(console.warn("[ErrorReportingService] Rate limit exceeded, suppressing error report"),!1):(this.errorCache.set(n,t),!0)}_createErrorKey(e){const t=[e.type||"UnknownError",(e.message||"").substring(0,100),e.source||e.filename||"unknown",e.lineno||0,e.colno||0];if(e.stack){const n=e.stack.split("\n").slice(0,3).join("|");t.push(n.substring(0,100))}return t.join("::")}_cleanupErrorCache(e){let t=0;this.errorCache.forEach(((n,r)=>{e-n>this.errorTimeWindow&&(this.errorCache.delete(r),t++)})),t>0&&console.debug(`[ErrorReportingService] Cleaned up ${t} old error entries`)}_formatForDiscord(e){let t=`${this._getErrorEmoji(e.type)} **${e.type||"Error"}**\n`;e.message&&(t+=`\ud83d\udcdd **Message:**\n\`\`\`\n${e.message}\n\`\`\`\n`);const n=this._formatLocation(e);if(n&&(t+=`\ud83d\udccd **Location:** ${n}\n`),e.stack){const n=this._cleanStackTrace(e.stack);n&&(t+=`\ud83d\udccb **Stack Trace:**\n\`\`\`javascript\n${n}\n\`\`\`\n`)}const r=(new Date).toLocaleString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1});if(t+=`\u23f0 ${r}`,t.length>2e3){const e=t.split("\n"),n=e.slice(0,3).join("\n"),o=`\u23f0 ${r}`,a=2e3-n.length-o.length-50;if(a>100){t=`${n}\n${e.slice(3,-1).join("\n").substring(0,a)}\n...\n${o}`}else t=`${n}\n*Stack trace too long to display*\n${o}`}return t}_getErrorEmoji(e){return{"Uncaught Error":"\ud83d\udea8","Error Event":"\u26a0\ufe0f","Unhandled Promise Rejection":"\ud83d\udd25","Console Error":"\ud83d\udce2","Manual Report":"\ud83d\udc1b"}[e]||"\u274c"}_formatLocation(e){const t=e.source||e.filename;if(!t&&!e.lineno)return null;let n="";if(t){n=`\`${t.split("/").pop().split("\\").pop()}\``}return e.lineno&&(n+=` line ${e.lineno}`,e.colno&&(n+=`:${e.colno}`)),n}_cleanStackTrace(e){if(!e)return null;const t=e.split("\n").map((e=>e.trim())).filter((e=>e&&!e.includes("chrome-extension://"))).slice(0,8).join("\n");return t.length>this.maxStackLength?t.substring(0,this.maxStackLength)+"\n... (truncated)":t}async reportError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{if(t&&!this._shouldReportError(t))return;const n="string"===typeof e?e:JSON.stringify(e),r=await _(n);r.ok||console.warn(`Failed to report error to API: HTTP ${r.status}`)}catch(n){console.warn("Error reporting to API:",n.message)}}_setupErrorHandlers(){var e=this;window.onerror=(e,t,n,r,o)=>{const a={type:"Uncaught Error",message:e,source:t,lineno:n,colno:r,stack:(null===o||void 0===o?void 0:o.stack)||"No stack trace"},l=this._formatForDiscord(a);return this.reportError(l,a),!1},window.addEventListener("error",(e=>{var t;if("Script error."===e.message&&!e.error)return;const n={type:"Error Event",message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,stack:(null===(t=e.error)||void 0===t?void 0:t.stack)||"No stack trace"},r=this._formatForDiscord(n);this.reportError(r,n)})),window.addEventListener("unhandledrejection",(e=>{const t=e.reason,n={type:"Unhandled Promise Rejection",message:(null===t||void 0===t?void 0:t.message)||String(t),stack:(null===t||void 0===t?void 0:t.stack)||"No stack trace"},r=this._formatForDiscord(n);this.reportError(r,n)}));const t=console.error;console.error=function(){let n="",r="No stack trace provided via console.error";const o=[];for(var a=arguments.length,l=new Array(a),i=0;i<a;i++)l[i]=arguments[i];l.forEach((e=>{if(e instanceof Error)o.push(e.message),r=e.stack||r;else try{o.push("object"===typeof e&&null!==e?JSON.stringify(e,null,2):String(e))}catch(t){o.push(String(e))}})),n=o.join(" ");const s={type:"Console Error",message:n,stack:r},c=e._formatForDiscord(s);e.reportError(c,s),t.apply(console,l)}}manualReport(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=e instanceof Error,r={type:"Manual Report",message:n?e.message:String(e),stack:n?e.stack||"No stack trace":"N/A (Not an Error object)",...t},o=this._formatForDiscord(r);this.reportError(o,r)}};class H extends r.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t});try{const n={type:"React Error Boundary",message:e.message,stack:e.stack,componentStack:t.componentStack,errorBoundary:this.constructor.name};W.manualReport(e,n)}catch(n){console.warn("Failed to report error through ErrorBoundary:",n)}this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback(this.state.error,this.handleRetry):(0,u.jsx)("div",{className:"min-h-screen bg-bg-base flex items-center justify-center p-4",children:(0,u.jsxs)("div",{className:"bg-bg-surface border border-border-l1 rounded-xl shadow-lg max-w-md w-full p-6",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,u.jsx)("div",{className:"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center",children:(0,u.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,u.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h2",{className:"text-lg font-semibold text-span-primary",children:"Something went wrong"}),(0,u.jsx)("p",{className:"text-sm text-span-muted",children:"The application encountered an error"})]})]}),(0,u.jsxs)("div",{className:"mb-4",children:[(0,u.jsx)("p",{className:"text-span-secondary text-sm leading-relaxed mb-3",children:"We've encountered an unexpected error. This has been automatically reported to our development team."}),(0,u.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-span-muted mb-3",children:[(0,u.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,u.jsx)("span",{children:"Error automatically reported to developers"})]})]}),this.state.error&&(0,u.jsxs)("details",{className:"mb-4",children:[(0,u.jsx)("summary",{className:"text-xs text-span-muted cursor-pointer hover:text-span-secondary transition-colors select-none",children:"Technical details (click to expand)"}),(0,u.jsx)("div",{className:"mt-2 p-3 bg-bg-hover rounded-lg border border-border-l1",children:(0,u.jsx)("code",{className:"text-xs text-span-muted font-mono break-all",children:this.state.error.message})})]}),(0,u.jsxs)("div",{className:"flex space-x-3",children:[(0,u.jsx)("button",{onClick:this.handleRetry,className:"flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-primary/50",children:"Try Again"}),(0,u.jsx)("button",{onClick:()=>window.location.reload(),className:"flex-1 px-4 py-2 bg-bg-hover text-span-secondary border border-border-l1 rounded-lg hover:bg-bg-surface transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-border-l1",children:"Reload App"})]})]})}):this.props.children}}const q=H,Q=e=>{let{isVisible:t,onClose:n,version:o}=e;(0,r.useRef)(null);const a=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(!t)return;const e=e=>{"Escape"===e.key&&n()};return a.current&&a.current.focus(),document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[t,n]),t?(0,u.jsxs)("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center transition-all duration-300 "+(t?"opacity-100":"opacity-0 pointer-events-none"),children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-900/80 via-indigo-900/80 to-blue-900/80"}),(0,u.jsx)("div",{className:"absolute inset-0 opacity-[0.07]",style:{backgroundImage:"url('https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/grid-pattern.png')"}}),(0,u.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(93,123,255,0.15),transparent_50%)]"}),(0,u.jsx)("div",{className:"relative z-[9999] w-[90%] max-w-md transform transition-all duration-300 scale-100",children:(0,u.jsxs)("div",{className:"relative overflow-hidden rounded-2xl shadow-2xl",children:[(0,u.jsxs)("div",{className:"absolute inset-0",children:[(0,u.jsx)("div",{className:"w-full h-full bg-repeat opacity-20",style:{backgroundImage:"url(https://raw.githubusercontent.com/cydolo/assets/main/pluto/background.png)"}}),(0,u.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-[#2658C7] to-[#2456C3]"})]}),(0,u.jsxs)("div",{className:"relative pt-6 px-6 pb-6",children:[(0,u.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)("div",{className:"absolute inset-0 bg-blue-500/30 blur-sm rounded-full"}),(0,u.jsx)("img",{src:"https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/logo.png",alt:"Pluto Logo",className:"relative h-8 w-8"})]}),(0,u.jsx)("div",{className:"flex-1",children:(0,u.jsx)("span",{className:"text-base font-bold text-white tracking-wide",children:"Toolbox Updated"})})]}),(0,u.jsxs)("p",{className:"text-blue-100/90 text-sm leading-relaxed mb-6",children:["We've updated our toolbox to version ",o," with new features and improvements for a better experience. Thanks for using Pluto! \u2764\ufe0f"]}),(0,u.jsx)("div",{className:"flex justify-center",children:(0,u.jsx)("button",{ref:a,onClick:n,className:"group flex items-center gap-2 px-6 py-2 rounded-lg bg-[#2E66D8]/80 hover:bg-[#2E66D8] border border-white/10 transition-all duration-200 transform hover:scale-105 shadow-lg focus:outline-none focus:ring-2 focus:ring-white/20",children:(0,u.jsx)("span",{className:"text-sm font-medium text-white",children:"Got it"})})})]})]})})]}):null};const K=new class{constructor(){this.STORAGE_KEY="pluto-toolbox-notifications",this.LAST_SEEN_VERSION_KEY="pluto-toolbox-last-seen-version",this.AUTO_FETCH_INTERVAL=6e4,this.intervalId=null,this.onUpdateCallback=null}init(e,t){this.onUpdateCallback=e,this.onHistoryUpdateCallback=t,this.startAutoFetch(),this.checkForNewUpdates()}startAutoFetch(){this.intervalId&&clearInterval(this.intervalId),this.intervalId=setInterval((()=>{this.checkForNewUpdates()}),this.AUTO_FETCH_INTERVAL),console.info("[NotificationService] Auto-fetch started (1 minute interval)")}stopAutoFetch(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null,console.info("[NotificationService] Auto-fetch stopped"))}async checkForNewUpdates(){try{console.debug("[NotificationService] Checking for new updates...");const e=await T(!0),t=this.getLastSeenVersion();console.debug(`[NotificationService] Last seen version: ${t||"none"}`);const n=this.findNewUpdates(e.toolbox.changelog,t),r=[];for(const a of e.tools){const e=this.findNewUpdates(a.changelog,t);e.length>0&&r.push({toolName:a.name,toolVersion:a.version,updates:e})}const o=[...n.map((e=>({...e,source:"toolbox"}))),...r.flatMap((e=>e.updates.map((t=>({...t,source:"tool",toolName:e.toolName,toolVersion:e.toolVersion})))))];o.length>0?(console.debug(`[NotificationService] Found ${o.length} new updates since ${t||"never"}`),this.storeNotifications(o),this.onUpdateCallback&&this.onUpdateCallback(o.length),this.onHistoryUpdateCallback&&this.onHistoryUpdateCallback(e)):(console.debug("[NotificationService] No new updates found"),this.onHistoryUpdateCallback&&this.onHistoryUpdateCallback(e))}catch(e){console.warn("[NotificationService] Failed to check for updates:",e.message)}}findNewUpdates(e,t){if(!e||0===e.length)return[];const n=e.sort(((e,t)=>{const n=e.Version||e.version,r=t.Version||t.version;if(n&&r){if(this.isVersionNewer(r,n))return 1;if(this.isVersionNewer(n,r))return-1}const o=new Date(e.ReleasedAt||e.releasedAt||0);return new Date(t.ReleasedAt||t.releasedAt||0)-o}));if(!t){const e=n[0];return console.debug(`[NotificationService] No last seen version, considering latest as new: ${(null===e||void 0===e?void 0:e.Version)||(null===e||void 0===e?void 0:e.version)}`),e?[e]:[]}const r=[];for(const o of n){const e=o.Version||o.version;e&&this.isVersionNewer(e,t)&&(r.push(o),console.debug(`[NotificationService] Found new update: ${e} (newer than ${t})`))}return r}isVersionNewer(e,t){const n=e.split(".").map(Number),r=t.split(".").map(Number);for(let o=0;o<Math.max(n.length,r.length);o++){const e=n[o]||0,t=r[o]||0;if(e>t)return!0;if(e<t)return!1}return!1}getLastSeenVersion(){try{return localStorage.getItem(this.LAST_SEEN_VERSION_KEY)}catch(e){return console.warn("[NotificationService] Could not read last seen version:",e.message),null}}setLastSeenVersion(e){try{localStorage.setItem(this.LAST_SEEN_VERSION_KEY,e),console.debug(`[NotificationService] Marked version ${e} as seen`)}catch(t){console.warn("[NotificationService] Could not store last seen version:",t.message)}}storeNotifications(e){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e))}catch(t){console.warn("[NotificationService] Could not store notifications:",t.message)}}getStoredNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);return e?JSON.parse(e):[]}catch(e){return console.warn("[NotificationService] Could not read stored notifications:",e.message),[]}}async markUpdatesAsViewed(){try{console.debug("[NotificationService] Marking updates as viewed...");let t=null;try{const e=await T(!0);t=this.findAbsoluteLatestVersion(e)}catch(e){console.warn("[NotificationService] Could not fetch latest data, using stored notifications");const n=this.getStoredNotifications();if(n.length>0){const e=n.sort(((e,t)=>{const n=e.Version||e.version,r=t.Version||t.version;return this.isVersionNewer(r,n)?1:-1}));t=e[0].Version||e[0].version}}t&&(this.setLastSeenVersion(t),console.debug(`[NotificationService] Marked version ${t} as last seen`)),this.storeNotifications([]),this.onUpdateCallback&&this.onUpdateCallback(0),console.debug("[NotificationService] Updates marked as viewed, notifications cleared")}catch(e){console.warn("[NotificationService] Could not mark updates as viewed:",e.message)}}findAbsoluteLatestVersion(e){let t=null;if(e.toolbox&&e.toolbox.changelog)for(const n of e.toolbox.changelog){const e=n.Version||n.version;!e||t&&!this.isVersionNewer(e,t)||(t=e)}if(e.tools)for(const n of e.tools)if(n.changelog)for(const e of n.changelog){const n=e.Version||e.version;!n||t&&!this.isVersionNewer(n,t)||(t=n)}return console.debug(`[NotificationService] Found absolute latest version: ${t}`),t}getNotificationCount(){return this.getStoredNotifications().length}destroy(){this.stopAutoFetch(),this.onUpdateCallback=null,this.onHistoryUpdateCallback=null}};const Y=function(){const[e,t]=(0,r.useState)("tools"),[n,o]=(0,r.useState)(null),[l,i]=(0,r.useState)([]),[s,c]=(0,r.useState)([]),[m,h]=(0,r.useState)(null),[g,v]=(0,r.useState)(0),[b,y]=(0,r.useState)(!1),[w,x]=(0,r.useState)(!0),[k,S]=(0,r.useState)(!1),[_,$]=(0,r.useState)(!1),[O,I]=(0,r.useState)(!1),[H,Y]=(0,r.useState)(!1),[G,X]=(0,r.useState)(new Set),[J,Z]=(0,r.useState)(null),[ee,te]=(0,r.useState)(!1),[ne,re]=(0,r.useState)({enabled:!1,message:"",estimatedDuration:"Unknown"}),[oe,ae]=(0,r.useState)({available:!1,currentVersion:p,latestVersion:"0.0.0",downloadUrl:null,changelog:[]}),[le,ie]=(0,r.useState)("Initializing..."),se=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown";console.error(`[App] Global error in ${t}:`,e);const n=(null===e||void 0===e?void 0:e.message)||(null===e||void 0===e?void 0:e.toString())||"An unexpected error occurred";Z(n),te(!0),I(!1),Y(!1);try{W.reportError(e,t)}catch(r){console.warn("[App] Could not report error:",r.message)}},ce=e=>{X((t=>new Set([...t,e])))},ue=e=>{X((t=>{const n=new Set(t);return n.delete(e),n}))},de=G.size>0;(0,r.useEffect)((()=>{const e=localStorage.getItem("pluto-theme")||"dark";document.documentElement.setAttribute("data-theme",e);const t=e=>{se(e.error||new Error(e.message),"Unhandled Error")},n=e=>{se(e.reason||new Error("Unhandled Promise Rejection"),"Promise Rejection")};return window.addEventListener("error",t),window.addEventListener("unhandledrejection",n),()=>{window.removeEventListener("error",t),window.removeEventListener("unhandledrejection",n)}}),[]),(0,r.useEffect)((()=>{k&&setTimeout((()=>{try{if("undefined"!==typeof a&&a.window){const e=document.getElementById("resize-handle-right"),t=document.getElementById("resize-handle-bottom"),n=document.getElementById("resize-handle-corner");let r=!1,o=0,l=0,i=0,s=0,c="",u=0,d=null,f=null;const p=(e,t)=>{e.preventDefault(),r=!0,c=t,o=e.clientX,l=e.clientY,a.window.getSize().then((e=>{i=e.width,s=e.height,d||(d=requestAnimationFrame(h))})).catch((e=>{console.error("[App] Error getting window size:",e)})),document.addEventListener("mousemove",m),document.addEventListener("mouseup",g),document.body.classList.add("resizing")},m=e=>{r&&(f={clientX:e.clientX,clientY:e.clientY})},h=()=>{if(r&&f){const e=Date.now();if(e-u>=16){const{clientX:t,clientY:n}=f,r=t-o,d=n-l;let p=i,m=s;c.includes("e")&&(p=Math.max(i+r,535)),c.includes("s")&&(m=Math.max(s+d,615)),a.window.setSize({width:p,height:m}).catch((e=>{console.error("[App] Error resizing window:",e)})),u=e}}r&&(d=requestAnimationFrame(h))},g=()=>{r=!1,d&&(cancelAnimationFrame(d),d=null),document.removeEventListener("mousemove",m),document.removeEventListener("mouseup",g),document.body.classList.remove("resizing")};e&&e.addEventListener("mousedown",(e=>p(e,"e"))),t&&t.addEventListener("mousedown",(e=>p(e,"s"))),n&&n.addEventListener("mousedown",(e=>p(e,"se"))),console.info("[App] Window resize handlers initialized")}else console.warn("[App] Neutralino window API not available for resize handlers")}catch(e){console.error("[App] Error initializing resize handlers:",e)}}),500)}),[k]),(0,r.useEffect)((()=>{(async()=>{if(!_)try{W.init(),await f.init();const e=await N(!0),t=await j(!1),n=await C(!1),r=await P(!1),a=await T(!1);console.log("Fetched data:",e),o(e),re(t),ae({available:n.currentVersion!==n.latestVersion,currentVersion:p,latestVersion:n.latestVersion,downloadUrl:n.downloadUrl,changelog:n.changelog}),i(r),h(a);const l=await E(),s=null===e||void 0===e?void 0:e[l];s.changelog&&Array.isArray(s.changelog)&&c(s.changelog),$(!0),ie("System ready")}catch(e){console.error("Data fetching error:",e),se(e,"App Initialization"),ie("Error initializing system - using offline mode"),$(!0)}})()}),[_]),(0,r.useEffect)((()=>()=>{K.destroy()}),[]);const fe=()=>{console.log("Initialization animation complete, showing main UI"),x(!1),S(!0),pe(),K.init((e=>{v(e)}),(e=>{h(e),c(e.toolbox.changelog),console.debug("[App] Comprehensive changelog refreshed with new data")}))},pe=()=>{try{const e="pluto-toolbox-last-notified-version";localStorage.getItem(e)!==p&&setTimeout((()=>{y(!0),localStorage.setItem(e,p)}),500)}catch(e){console.warn("[App] Could not check update notification status:",e)}};return w?(0,u.jsx)(D,{onInitializationComplete:fe}):(0,u.jsxs)(q,{onError:se,children:[(0,u.jsxs)("div",{className:"relative h-screen flex flex-col bg-transparent overflow-hidden",children:[ne.enabled&&(0,u.jsx)("div",{className:"popup-container",children:(0,u.jsx)(R,{maintenanceInfo:ne,onMaintenanceRefresh:async()=>{try{const e=await j(!0);return re(e),e}catch(e){return console.error("Failed to refresh maintenance status:",e),ne}}})}),oe.available&&(0,u.jsx)("div",{className:"popup-container",children:(0,u.jsx)(U,{updateInfo:oe})}),(0,u.jsx)(Q,{isVisible:b,onClose:()=>{y(!1)},version:p}),(0,u.jsx)(d,{currentTab:e,setCurrentTab:async e=>{if(t(e),"updates"===e&&g>0)try{await K.markUpdatesAsViewed()}catch(n){console.warn("[App] Failed to mark updates as viewed:",n.message)}},toolboxVersion:p,statusMessage:le,updateNotificationCount:g}),(0,u.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col bg-bg-base",children:[(0,u.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,u.jsx)(F,{isVisible:"tools"===e,tools:l,isLoading:O,hasActiveOperations:de,addOperation:ce,removeOperation:ue}),(0,u.jsx)(A,{isVisible:"updates"===e,updateHistory:s,comprehensiveChangelog:m,isLoading:H})]}),(0,u.jsx)(M,{onRefreshTools:async()=>{const e="refresh-tools";ce(e),I(!0),await L.delay(2e3),ie("Refreshing tools...");try{const e=await P(!0);i(e);const t=await Promise.all(e.map((async e=>{const t=e.id||e.name,n=await z.isToolInstalled(t),r=n?await z.getInstalledToolVersion(t):null;return{...e,isInstalled:n,hasUpdate:n&&r!==e.version}})));i(t),ie("Tools refreshed successfully"),setTimeout((()=>ie("System ready")),2e3)}catch(t){console.error("Failed to refresh tools:",t),se(t,"Tools Refresh"),ie("Error refreshing tools - using cached data"),setTimeout((()=>ie("System ready")),2e3)}finally{I(!1),ue(e)}},onRefreshUpdates:async()=>{const e="refresh-updates";ce(e),Y(!0),await L.delay(2e3),ie("Refreshing updates...");try{const e=await N(!0),t=await E(),n=null===e||void 0===e?void 0:e[t];null!==n&&void 0!==n&&n.changelog&&Array.isArray(n.changelog)&&c(n.changelog);const r=await C(!0);ae({available:r.currentVersion!==r.latestVersion,currentVersion:r.currentVersion,latestVersion:r.latestVersion,downloadUrl:r.downloadUrl,changelog:r.changelog}),ie("Updates refreshed successfully"),setTimeout((()=>ie("System ready")),2e3)}catch(t){console.error("Failed to refresh updates:",t),se(t,"Updates Refresh"),ie("Error refreshing updates - using cached data"),setTimeout((()=>ie("System ready")),2e3)}finally{Y(!1),ue(e)}},onCheckUpdates:async()=>{const e="check-updates";ce(e),ie("Checking for updates...");try{const e=await C(!0),t=e.currentVersion!==e.latestVersion;ae({available:t,currentVersion:e.currentVersion,latestVersion:e.latestVersion,downloadUrl:e.downloadUrl,changelog:e.changelog}),ie(t?"Update available!":"No updates available"),setTimeout((()=>ie("System ready")),2e3)}catch(t){console.error("Failed to check for updates:",t),ie("Error checking for updates"),setTimeout((()=>ie("System ready")),2e3)}finally{ue(e)}},hasActiveOperations:de})]})]}),(0,u.jsx)(B,{onTriggerError:se}),(0,u.jsx)(V,{isVisible:ee,errorMessage:J,onClose:()=>{te(!1),Z(null)}})]})};document.addEventListener("contextmenu",(e=>(e.preventDefault(),!1)),{capture:!0});o.createRoot(document.getElementById("root")).render((0,u.jsx)(Y,{})),console.info("[Neutralino] Initializing Neutralino..."),(0,a.init)()})();
//# sourceMappingURL=main.4af17a56.js.map