{"version": 3, "file": "static/css/main.f73bc471.css", "mappings": "AACA,kBACI,MACI,uBACJ,CACA,IACI,0BACJ,CACJ,CAGA,gBACI,GACI,sBACJ,CACA,GACI,uBACJ,CACJ,CAGA,gBACI,GAEI,SAAU,CADV,kBAEJ,CACA,OAEI,SAAU,CADV,oBAEJ,CACJ,CAGA,iBACI,MACI,SACJ,CACA,IACI,WACJ,CACJ,CAGA,kBACI,GACI,SACJ,CACA,GACI,SACJ,CACJ,CAGA,0BACI,GAEI,SAAU,CADV,2BAEJ,CACA,GAEI,SAAU,CADV,uBAEJ,CACJ,CAGA,cACI,iCACJ,CAEA,cACI,mDACJ,CAEA,eACI,mDACJ,CAEA,gBACI,4BACJ,CAEA,iBACI,sCACJ,CAEA,kBACI,8CACJ,CCtFA,qCAEI,UAAW,CADX,SAEJ,CAEA,2CACI,gBAAuB,CACvB,iBAAkB,CAClB,UACJ,CAEA,2CACI,0BAA6D,CAA7D,iDAA6D,CAE7D,0BAA4D,CAA5D,iDAA4D,CAD5D,iBAAkB,CAElB,mCAA6B,CAA7B,2BACJ,CAEA,iDACI,0BAA+D,CAA/D,+CACJ,CAGA,kBAEI,+BAAwE,CAAxE,sDAAwE,CADxE,oBAEJ,CAEA,kBACI,wCAAyC,CAEzC,cAAe,CADf,SAAU,CAEV,mBACJ,CAEA,4BACI,oBACJ,CAOA,oEACI,mBACJ,CAGA,iBACI,iEAA6F,CAC7F,yEACJ,CAGA,2BACI,iCACJ,CC1DA,iBACI,wDAG8B,CAC9B,gEAIJ,CAGA,KAII,kCAAwC,CAExC,mDACJ,CAGA,EACI,6FACJ,CAGA,sBACI,8BAA+B,CAC/B,+BACJ,CAGA,YAII,kCAA2B,CAA3B,0BAA2B,CAH3B,kBAAmB,CACnB,sCAAiD,CACjD,eAEJ,CAOA,kBAMI,kBAAmB,CAFnB,wBAA0D,CAA1D,6CAA0D,CAK1D,kBAAmB,CAJnB,YAAa,CAHb,OAAQ,CAKR,sBAAuB,CANvB,cAAe,CAOf,qDAAyD,CALzD,YAOJ,CAEA,yBACI,YACJ,CAEA,4BACI,SACJ,CAGA,cAEI,eAAgB,CADhB,iBAEJ,CAEA,oBAOI,mDAAmF,CANnF,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAKN,2BAA4B,CAC5B,iCAAmC,CAJnC,UAKJ,CAEA,0BACI,0BACJ,CAGA,wBACI,GAEI,SAAU,CADV,0BAEJ,CACA,GAEI,SAAU,CADV,uBAEJ,CACJ,CAEA,yBACI,GAEI,SAAU,CADV,uBAEJ,CACA,GAEI,SAAU,CADV,0BAEJ,CACJ,CAEA,8BACI,YACJ,CAEA,oBACI,8DAAmF,CACnF,eACJ,CAGA,yBACI,MACI,4BACJ,CACA,IACI,8BACJ,CACJ,CAEA,mCACI,mCACJ,CAGA,uBACI,uBACJ,CAEA,6BAEI,4DAA0F,CAD1F,qBAEJ,CAEA,qBACI,8DACJ,CAGA,oCACI,sBACJ,CAGA,gBAEE,WAAY,CAIZ,mBAAoB,CALpB,cAAe,CAEf,UAAW,CAEX,0CAAiD,CADjD,YAGF,CAGA,uBACE,gBAAiB,CACjB,wBACF,CAGA,yCACE,0BACF,CAEA,0CACE,uBACF,CAGA,mDACI,SACJ,CAMA,wGACI,SACJ,CAGA,6BACI,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACtC,CAEA,sBACI,qDACJ,CAEA,4BACI,uBACJ,CAEA,6BACI,2CACJ,CAGA,+HASI,6FACJ,CCrNA,MACE,uBAAmC,CACnC,uBAAsC,CACtC,gCAA4C,CAC5C,0BAAqC,CAErC,2BAAsC,CACtC,2BAAsC,CAEtC,8BAA4C,CAC5C,4BAAyC,CACzC,4BAA0C,CAE1C,uBAAkC,CAClC,uBAAiC,CACjC,uBAAkC,CAClC,qBACF,CAGA,KACE,4BAAgC,CAChC,gBAAiB,CACjB,eACF,CAGA,OACE,sBACF,CAGA,kDAGE,0BAAmE,CAAnE,iDAAmE,CACnE,2BACF,CAGA,6BACE,sBAA8D,CAA9D,6CACF,CAGA,8BACE,0BACF,CAGA,cAEE,oBAAqB,CACrB,YAGF,CAEA,4BAJE,2BAA4B,CAH5B,mBAAoB,CAIpB,eASF,CANA,cAEE,oBAAqB,CACrB,YAGF,CAIA,iBACE,mBACF,CAGA,YACE,WACF,CAIA,iBAIE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAD1B,sBAAoC,CAHpC,YAAa,CADb,OAAQ,CAGR,sBAAuB,CAJvB,iBAAkB,CAOlB,YACF", "sources": ["styles/animation.css", "styles/scrollbar.css", "styles/main.css", "styles/globals.css"], "sourcesContent": ["/* Scroll indicator animation */\r\n@keyframes bounce {\r\n    0%, 100% {\r\n        transform: translateY(0);\r\n    }\r\n    50% {\r\n        transform: translateY(-5px);\r\n    }\r\n}\r\n\r\n/* Loading spinner animation */\r\n@keyframes spin {\r\n    0% {\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n/* Ping animation for initialization screen */\r\n@keyframes ping {\r\n    0% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n    75%, 100% {\r\n        transform: scale(1.5);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n/* Pulse animation for status indicators - optimized for both themes */\r\n@keyframes pulse {\r\n    0%, 100% {\r\n        opacity: 1;\r\n    }\r\n    50% {\r\n        opacity: 0.35; /* Higher contrast for better visibility in both themes */\r\n    }\r\n}\r\n\r\n/* Fade in animation */\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n    to {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Slide in from top animation */\r\n@keyframes slideInFromTop {\r\n    from {\r\n        transform: translateY(-20px);\r\n        opacity: 0;\r\n    }\r\n    to {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Apply animations to elements */\r\n.animate-spin {\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n.animate-ping {\r\n    animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;\r\n}\r\n\r\n.animate-pulse {\r\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n.animate-bounce {\r\n    animation: bounce 1s infinite;\r\n}\r\n\r\n.animate-fade-in {\r\n    animation: fadeIn 0.5s ease-out forwards;\r\n}\r\n\r\n.animate-slide-in {\r\n    animation: slideInFromTop 0.5s ease-out forwards;\r\n}", "/* Custom scrollbar styling - updated for light mode UI system */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n    width: 6px;\r\n    height: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n    background: transparent;\r\n    border-radius: 6px;\r\n    margin: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n    background-color: var(--color-border-l1, rgba(0, 0, 0, 0.15));\r\n    border-radius: 6px;\r\n    border: 1px solid var(--color-border-l1, rgba(0, 0, 0, 0.1));\r\n    transition: all 0.2s ease-out;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n    background-color: var(--color-primary, rgba(36, 144, 172, 0.6));\r\n}\r\n\r\n/* Firefox scrollbar */\r\n.custom-scrollbar {\r\n    scrollbar-width: thin;\r\n    scrollbar-color: var(--color-border-l1, rgba(0, 0, 0, 0.15)) transparent;\r\n}\r\n\r\n.scroll-indicator {\r\n    animation: bounce 2s infinite ease-in-out;\r\n    opacity: 0;\r\n    cursor: pointer;\r\n    pointer-events: auto;\r\n}\r\n\r\n.scroll-indicator:hover svg {\r\n    transform: scale(1.2);\r\n}\r\n\r\n/* Hide scroll indicators when user has scrolled to end */\r\n.scroll-end .scroll-down-indicator {\r\n    opacity: 0 !important;\r\n}\r\n\r\n.scroll-top .scroll-up-indicator {\r\n    opacity: 0 !important;\r\n}\r\n\r\n/* Fancy fade mask for tools container */\r\n.tools-fade-mask {\r\n    mask-image: linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%);\r\n    -webkit-mask-image: linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%);\r\n}\r\n\r\n/* Scrolling styles */\r\n.scrolling-fast .tool-card {\r\n    transition: transform 0.1s ease-out;\r\n}", ".tools-fade-mask {\r\n    mask-image: linear-gradient(to bottom,\r\n            rgba(0, 0, 0, 1) 0%,\r\n            rgba(0, 0, 0, 1) 85%,\r\n            rgba(0, 0, 0, 0) 100%);\r\n    -webkit-mask-image: linear-gradient(to bottom,\r\n            rgba(0, 0, 0, 1) 0%,\r\n            rgba(0, 0, 0, 1) 85%,\r\n            rgba(0, 0, 0, 0) 100%);\r\n}\r\n\r\n/* Main styles for Pluto Toolbox */\r\nbody {\r\n    font-family: 'Inter', sans-serif;\r\n    min-height: 100vh;\r\n    overflow: hidden;\r\n    background-color: transparent !important;\r\n    /* Add transition for theme changes */\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\n}\r\n\r\n/* Apply transitions to all elements that use theme variables */\r\n* {\r\n    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;\r\n}\r\n\r\n/* Make content area have rounded bottom corners */\r\n.status-bar-container {\r\n    border-bottom-left-radius: 1rem;\r\n    border-bottom-right-radius: 1rem;\r\n}\r\n\r\n/* Custom window styles */\r\n.app-window {\r\n    border-radius: 1rem;\r\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\r\n    overflow: hidden;\r\n    backdrop-filter: blur(20px);\r\n}\r\n\r\n/* Resize handle styles removed as now using default window functionality */\r\n\r\n/* Titlebar draggable styles removed as now using default window functionality */\r\n\r\n/* Preloader Styles - updated for theme switching */\r\n#system-preloader {\r\n    position: fixed;\r\n    inset: 0;\r\n    z-index: 9999;\r\n    background-color: var(--color-bg-base, rgb(249, 250, 251)); /* Use theme variable */\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: opacity 0.3s ease, background-color 0.3s ease;\r\n    border-radius: 1rem;\r\n}\r\n\r\n#system-preloader.hidden {\r\n    display: none;\r\n}\r\n\r\n#system-preloader.opacity-0 {\r\n    opacity: 0;\r\n}\r\n\r\n/* Shine effect for buttons */\r\n.shine-effect {\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.shine-effect::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\r\n    transform: translateX(-100%);\r\n    transition: transform 0.5s ease-out;\r\n}\r\n\r\n.shine-effect:hover::after {\r\n    transform: translateX(100%);\r\n}\r\n\r\n/* Error notification animations */\r\n@keyframes slideInRight {\r\n    from {\r\n        transform: translateX(100%);\r\n        opacity: 0;\r\n    }\r\n    to {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes slideOutRight {\r\n    from {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n    to {\r\n        transform: translateX(100%);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n#error-notification-container {\r\n    z-index: 9999;\r\n}\r\n\r\n.error-notification {\r\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n    max-width: 400px;\r\n}\r\n\r\n/* Critical error animation - adjusted for theme compatibility */\r\n@keyframes criticalPulse {\r\n    0%, 100% {\r\n        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.3);\r\n    }\r\n    50% {\r\n        box-shadow: 0 0 0 8px rgba(220, 38, 38, 0);\r\n    }\r\n}\r\n\r\n.error-notification.border-red-600 {\r\n    animation: criticalPulse 2s infinite;\r\n}\r\n\r\n/* Error test dropdown styling */\r\n#error-test-btn button {\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n#error-test-btn button:hover {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 4px 6px -1px rgba(124, 58, 237, 0.2), 0 2px 4px -1px rgba(124, 58, 237, 0.1);\r\n}\r\n\r\n#error-test-dropdown {\r\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* Error test button visibility based on devMode */\r\nbody:not(.dev-mode) #error-test-btn {\r\n    display: none !important;\r\n}\r\n\r\n/* DevMode Panel Styles */\r\n.dev-mode-panel {\r\n  position: fixed;\r\n  bottom: 1rem;\r\n  right: 1rem;\r\n  z-index: 9999; /* Higher than error notifications to ensure visibility */\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  pointer-events: auto;\r\n}\r\n\r\n/* Ensure DevMode panel doesn't interfere with other elements */\r\n.dev-mode-panel button {\r\n  user-select: none;\r\n  -webkit-user-select: none;\r\n}\r\n\r\n/* DevMode panel animation improvements */\r\n.dev-mode-panel .error-test-button:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.dev-mode-panel .error-test-button:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n/* System/App States */\r\n#refresh-maintenance-button.checking .spinner-icon {\r\n    opacity: 1;\r\n}\r\n\r\n#refresh-maintenance-button:not(.checking) .check-icon {\r\n    opacity: 0;\r\n}\r\n\r\n#refresh-maintenance-button.checking .check-icon {\r\n    opacity: 0;\r\n}\r\n\r\n/* Theme toggle button animation */\r\n@keyframes themeToggleRotate {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n}\r\n\r\n.theme-toggle-btn svg {\r\n    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);\r\n}\r\n\r\n.theme-toggle-btn:hover svg {\r\n    transform: rotate(45deg);\r\n}\r\n\r\n.theme-toggle-btn:active svg {\r\n    animation: themeToggleRotate 0.5s ease-in-out;\r\n}\r\n\r\n/* Ensure proper color transitions when switching themes */\r\n.app-window,\r\n.status-bar-container,\r\nbutton,\r\n.tool-card,\r\n.error-notification,\r\n.uninstall-button,\r\n.tool-menu-button,\r\n.pluto-button,\r\n.btn {\r\n    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;\r\n}", "/* Import custom stylesheets */\r\n@import './animation.css';\r\n@import './scrollbar.css';\r\n@import './main.css';\r\n\r\n/* UI Theme Variables */\r\n:root {\r\n  --color-bg-base: rgb(248, 250, 252);\r\n  --color-bg-surface: rgb(255, 255, 255);\r\n  --color-bg-surface-hover: rgb(241, 245, 249);\r\n  --color-bg-hover: rgba(0, 0, 0, 0.03);\r\n  \r\n  --color-border-l1: rgba(0, 0, 0, 0.08);\r\n  --color-border-l2: rgba(0, 0, 0, 0.15);\r\n  \r\n  --color-span-default: rgba(15, 23, 42, 0.87);\r\n  --color-span-muted: rgba(15, 23, 42, 0.6);\r\n  --color-span-hover: rgba(15, 23, 42, 0.95);\r\n  \r\n  --color-primary: rgb(56, 114, 250);\r\n  --color-success: rgb(22, 163, 74);\r\n  --color-warning: rgb(251, 146, 60);\r\n  --color-error: rgb(239, 68, 68);\r\n}\r\n\r\n/* Global styles */\r\nbody {\r\n  font-family: 'Inter', sans-serif;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Override default button styles */\r\nbutton {\r\n  outline: none !important;\r\n}\r\n\r\n/* Default border styles for buttons in our UI system */\r\n.pluto-button,\r\n.tool-menu-button,\r\n.uninstall-button {\r\n  border: 1px solid var(--color-border-l1, rgba(156, 163, 175, 0.25));\r\n  transition: all 0.2s ease-out;\r\n}\r\n\r\n/* Global button hover state improvements */\r\nbutton:not([disabled]):hover {\r\n  border-color: var(--color-border-l2, rgba(156, 163, 175, 0.4));\r\n}\r\n\r\n/* Active state for all buttons */\r\nbutton:not([disabled]):active {\r\n  transform: translateY(0.5px);\r\n}\r\n\r\n/* Custom utility classes */\r\n.line-clamp-1 {\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.line-clamp-2 {\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n/* Tools-specific styles */\r\n#tools-container {\r\n  padding-bottom: 60px; /* Extra padding at the bottom for scrolling */\r\n}\r\n\r\n/* Custom opacity classes */\r\n.opacity-15 {\r\n  opacity: 0.15;\r\n}\r\n\r\n\r\n/* Popup container styles */\r\n.popup-container {\r\n  position: absolute;\r\n  inset: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: rgba(0, 0, 0, 0.4); /* Semi-transparent background */\r\n  backdrop-filter: blur(2px);\r\n  z-index: 9999; /* Ensure it appears above other elements */\r\n}"], "names": [], "sourceRoot": ""}