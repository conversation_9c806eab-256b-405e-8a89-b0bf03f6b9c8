import storageService from './storageService';
import neuService from './neuService';
import apiService from './apiService';
import downloadService from './downloadService';
import utilityService from './utilityService';

class ToolService {
  /**
   * Install a tool by downloading it to its specific folder in the .toolbox directory.
   * @param {Object} tool - Tool object containing name, version, and other details.
   * @param {Function} onProgress - Callback function to handle progress updates.
   * @returns {Promise<void>}
   */  async installTool(tool, onProgress) {
    const toolName = tool?.name || 'Unknown Tool';
    const toolId = tool?.id || tool?.name || 'unknown';
    let currentPhase = 'initialization';
    
    try {
      console.info(`[ToolService] Installing tool: ${toolName}`);

      const toolPath = storageService.getToolPath(toolId);
      const zipFilePath = `${toolPath}/${toolId}.zip`;

      // Clean up the tool directory
      currentPhase = 'cleanup';
      if (onProgress) {
        onProgress({ progress: 5, phase: 'Cleaning up directory...' });
      }
      await utilityService.cleanupDirectory(toolPath, (progressData) => {
        if (onProgress) {
          onProgress({
            progress: 5 + Math.floor(progressData.progress * 0.05),
            phase: progressData.phase
          });
        }
      });

      // Download the tool
      currentPhase = 'download';
      if (onProgress) {
        onProgress({ progress: 10, phase: 'Downloading tool...' });
      }
      // Use streaming approach for reliability
      await downloadService.downloadFile(tool.downloadUrl, zipFilePath, (progressData) => {
        if (onProgress) {
          onProgress({
            progress: 10 + Math.floor(progressData.progress * 0.70),
            phase: progressData.phase,
            loaded: progressData.loaded,
            total: progressData.total
          });
        }
      }, false); // Disable memory buffer approach for reliability

      // Extract the downloaded ZIP file
      currentPhase = 'extraction';
      if (onProgress) {
        onProgress({ progress: 85, phase: 'Extracting files...' });
      }
      await utilityService.extractZipFile(zipFilePath, toolPath, (progressData) => {
        if (onProgress) {
          onProgress({
            progress: 85 + Math.floor(progressData.progress * 0.10),
            phase: progressData.phase
          });
        }
      });

      // Validate and register tool
      currentPhase = 'registration';
      if (onProgress) {
        onProgress({ progress: 95, phase: 'Validating tool executable...' });
      }
      await this.addTool(toolId, toolName, tool.version);

      // Finalize installation
      if (onProgress) {
        onProgress({ progress: 100, phase: 'Installation complete!' });
      }

      console.info(`[ToolService] Tool installed successfully: ${toolName}`);
    } catch (error) {
      console.error(`[ToolService] Failed to install tool ${toolName} during ${currentPhase}:`, error.message);
      
      // Reset state and cleanup on failure
      await this._resetToolState(toolId, `Installation failed during ${currentPhase}`);
      
      const userFriendlyMessage = this._getUserFriendlyErrorMessage(error, currentPhase);
      
      if (onProgress) {
        onProgress({
          progress: 0,
          phase: userFriendlyMessage,
          error: userFriendlyMessage,
          resetState: true
        });
      }
      
      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * Check if a tool needs updating.
   * @param {string} toolId - ID of the tool to check.
   * @param {string} newVersion - New version to compare against.
   * @returns {Promise<boolean>} - True if the tool needs an update.
   */
  async needsUpdate(toolId, newVersion) {
    try {
      const currentVersion = await this.getInstalledToolVersion(toolId);
      if (!currentVersion) return false;
      
      // Simple version comparison (can be enhanced with semver)
      return currentVersion !== newVersion;
    } catch (error) {
      console.error(`[ToolService] Failed to check for updates for ${toolId}:`, error.message);
      return false;
    }
  }

  /**
   * Uninstall a tool by removing it from the storage and deleting its directory.
   * @param {string} toolId - ID of the tool to uninstall.
   * @returns {Promise<void>}
   */  async uninstallTool(toolId) {
    try {
      console.info(`[ToolService] Uninstalling tool: ${toolId}`);

      // Remove tool from storage and extensions file
      await this.removeTool(toolId);

      console.info(`[ToolService] Tool uninstalled successfully: ${toolId}`);
    } catch (error) {
      console.error(`[ToolService] Failed to uninstall tool ${toolId}:`, error.message);
      
      // Reset state even if uninstall partially failed
      await this._resetToolState(toolId, 'Uninstall encountered issues');
      
      const userFriendlyMessage = this._getUserFriendlyErrorMessage(error, 'uninstall');
      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * Add a new tool entry to the extensions.json file and create its directory.
   * Only adds the tool if the executable exists to prevent "executable not found" errors.
   * @param {string} id - Tool ID.
   * @param {string} name - Tool name.
   * @param {string} version - Tool version.
   * @returns {Promise<void>}
   */
  async addTool(id, name, version) {
    try {
      const toolPath = storageService.getToolPath(id);
      const toolExecutable = `${toolPath}/${id}.exe`;

      // Ensure tool directory exists
      const toolPathExists = await neuService.exists(toolPath);
      if (!toolPathExists) {
        console.info(`[ToolService] Creating directory for tool: ${toolPath}`);
        await neuService.createDirectory(toolPath);
      }

      // Validate that the tool executable exists before adding to extensions.json
      const executableExists = await neuService.exists(toolExecutable);
      if (!executableExists) {
        const errorMessage = `Tool executable not found: ${toolExecutable}. Tool will not be registered to prevent runtime errors.`;
        console.warn(`[ToolService] ${errorMessage}`);
        throw new Error(errorMessage);
      }

      console.info(`[ToolService] Tool executable validated: ${toolExecutable}`);

      const extensionsData = await storageService.readExtensionsFile();

      // Check if tool already exists, update it if so
      const existingToolIndex = extensionsData.extensions.findIndex(tool =>
        (tool.id?.toLowerCase() === id?.toLowerCase()) ||
        (tool.name?.toLowerCase() === name?.toLowerCase())
      );

      if (existingToolIndex >= 0) {
        extensionsData.extensions[existingToolIndex].version = version;
        console.info(`[ToolService] Updated existing tool ${name} to version ${version}`);
      } else {
        extensionsData.extensions.push({ id, name, version });
        console.info(`[ToolService] Added new tool ${name} version ${version} to extensions`);
      }

      await storageService.writeExtensionsFile(extensionsData);
    } catch (error) {
      console.error(`[ToolService] Failed to add tool ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Remove a tool entry from the extensions.json file and delete its directory.
   * @param {string} id - Tool ID.
   * @returns {Promise<void>}
   */
  async removeTool(id) {
    try {
      let extensionsData = await storageService.readExtensionsFile();
      
      // Initialize empty extensions data if null is returned
      if (!extensionsData) {
        extensionsData = { extensions: [] };
      } else if (!extensionsData.extensions) {
        extensionsData.extensions = [];
      }
      
      extensionsData.extensions = extensionsData.extensions.filter((tool) => {
        // Case-insensitive comparison for tool ID/name
        return tool.id?.toLowerCase() !== id?.toLowerCase() && 
               tool.name?.toLowerCase() !== id?.toLowerCase();
      });
      await storageService.writeExtensionsFile(extensionsData);

      const toolPath = storageService.getToolPath(id);
      const toolPathExists = await neuService.exists(toolPath);
      if (toolPathExists) {
        console.info(`[ToolService] Removing directory for tool: ${toolPath}`);
        await neuService.remove(toolPath);
      }
    } catch (error) {
      console.error(`[ToolService] Failed to remove tool ${id}:`, error.message);
      throw error;
    }
  }

  /**
   * Check if a tool is already installed.
   * @param {string} id - Tool ID or name.
   * @returns {Promise<boolean>} - True if the tool is installed, false otherwise.
   */
  async isToolInstalled(id) {
    try {
      const extensionsData = await storageService.readExtensionsFile();
      return extensionsData.extensions.some((tool) => {
        // Case-insensitive comparison for both id and name fields
        return (tool.id?.toLowerCase() === id?.toLowerCase()) || 
               (tool.name?.toLowerCase() === id?.toLowerCase());
      });
    } catch (error) {
      console.error(`[ToolService] Failed to check if tool is installed: ${id}`, error.message);
      throw error;
    }
  }

  /**
   * Get a list of all tools from the storage.
   * @returns {Promise<Array>} - List of tools stored in the extensions.json file.
   */
  async getTools() {
    try {
      const extensionsData = await storageService.readExtensionsFile();
      return extensionsData.extensions;
    } catch (error) {
      console.error('[ToolService] Failed to retrieve tools:', error.message);
      throw error;
    }
  }

  /**
   * Get the installed version of a tool.
   * @param {string} toolId - The ID or name of the tool.
   * @returns {Promise<string>} - The installed version of the tool.
   */
  async getInstalledToolVersion(toolId) {
    try {
      const tools = await this.getTools();
      // Use case-insensitive comparison for both id and name fields
      const tool = tools.find(t => 
        (t.id?.toLowerCase() === toolId?.toLowerCase()) || 
        (t.name?.toLowerCase() === toolId?.toLowerCase())
      );
      return tool ? tool.version : null;
    } catch (error) {
      console.error(`[ToolService] Failed to get installed version for tool ${toolId}:`, error.message);
      throw error;
    }
  }

  /**
   * Check if a tool is currently running
   * @param {string} toolId - ID of the tool to check
   * @returns {Promise<boolean>} - True if the tool is running, false otherwise
   */
  async isToolRunning(toolId) {
    try {
      console.debug(`[ToolService] Checking if tool is running: ${toolId}`);
      
      // First, check for running processes by executable name
      try {
        const toolProcesses = await neuService.findProcessesByName(`${toolId}.exe`);
        if (toolProcesses.length > 0) {
          console.debug(`[ToolService] Found ${toolProcesses.length} running processes for ${toolId}`);
          
          // If we found processes, update our local tracking with the new PID
          if (toolProcesses[0].pid) {
            this.storeToolProcessId(toolId, toolProcesses[0].pid);
          }
          
          return true;
        }
      } catch (e) {
        console.warn(`[ToolService] Error finding processes by name: ${e.message}`);
      }
      
      // As a fallback, check the PID we've stored
      try {
        const processesJson = localStorage.getItem('runningProcesses');
        if (processesJson) {
          const processes = JSON.parse(processesJson);
          const pid = processes[toolId];
          
          if (pid) {
            // Check if this process is still running
            const isRunning = await neuService.isProcessRunning(pid);
            
            // Clean up stale entries
            if (!isRunning) {
              delete processes[toolId];
              localStorage.setItem('runningProcesses', JSON.stringify(processes));
            } else {
              return true; // Process is still running
            }
          }
        }
      } catch (e) {
        console.warn('[ToolService] Error accessing stored process info:', e.message);
      }
      
      // If we reach here, no running process was found
      return false;
    } catch (error) {
      console.error(`[ToolService] Failed to check if tool ${toolId} is running:`, error.message);
      return false;
    }
  }

  /**
   * Store the process ID for a running tool
   * @param {string} toolId - ID of the tool
   * @param {number} pid - Process ID
   */
  async storeToolProcessId(toolId, pid) {
    try {
      if (!pid) {
        console.warn(`[ToolService] Attempted to store invalid PID for tool ${toolId}`);
        return;
      }
      
      console.debug(`[ToolService] Storing process ID ${pid} for tool ${toolId}`);
      
      // Store in localStorage as a fallback mechanism
      try {
        const processesJson = localStorage.getItem('runningProcesses');
        const processes = processesJson ? JSON.parse(processesJson) : {};
        processes[toolId] = pid;
        localStorage.setItem('runningProcesses', JSON.stringify(processes));
      } catch (e) {
        console.warn('[ToolService] Could not store process ID in localStorage:', e.message);
      }
    } catch (error) {
      console.error(`[ToolService] Failed to store process ID for tool ${toolId}:`, error.message);
    }
  }

  /**
   * Run a tool by spawning its process
   * @param {string} toolId - ID of the tool to run.
   * @returns {Promise<object>} - Result object containing success status and process ID.
   */
  async runTool(toolId) {
    try {
      const toolPath = storageService.getToolPath(toolId);
      const toolExecutable = `${toolPath}/${toolId}.exe`;

      // Check if the tool is already running
      const isAlreadyRunning = await this.isToolRunning(toolId);
      if (isAlreadyRunning) {
        console.info(`[ToolService] Tool ${toolId} is already running`);
        return { success: true, alreadyRunning: true };
      }

      // Check if the tool executable exists
      const toolExists = await neuService.exists(toolExecutable);
      if (!toolExists) {
        throw new Error(`Tool executable not found: ${toolExecutable}`);
      }

      // Use runExecutable which now implements an improved process detection loop
      console.info(`[ToolService] Starting tool: ${toolId}`);
      const processResult = await neuService.runExecutable(toolExecutable); 
      
      // Check if the launch was successful and we got a valid PID
      if (processResult.success) {
        if (processResult.pid) {
          console.info(`[ToolService] Tool process spawned successfully: ${toolId} with PID ${processResult.pid}`);
          await this.storeToolProcessId(toolId, processResult.pid);
          return { success: true, pid: processResult.pid };
        } else {
          // Handle case where process started but we couldn't find a PID
          // Continue execution instead of throwing an error, as the tool might still work
          console.warn(`[ToolService] Tool ${toolId} started but PID could not be determined. Will perform runtime checks.`);
          
          // Even without a PID, we'll do a final check in case the process appears after our internal timeout
          const isRunningAfterDelay = await this.isToolRunning(toolId);
          if (isRunningAfterDelay) {
            console.info(`[ToolService] Found ${toolId} running after additional runtime check.`);
            return { success: true, delayed: true };
          }
          
          // Continue anyway - ToolCard will poll for running status
          return { success: true, warning: "PID not found but tool may still be starting" };
        }
      } else {
        throw new Error(`Failed to start tool ${toolId}.`);
      }    } catch (error) {
      console.error(`[ToolService] Failed to run tool ${toolId}:`, error.message);
      
      // Reset tool state and provide user-friendly error
      await this._resetToolState(toolId, 'Failed to start tool');
      const userFriendlyMessage = this._getUserFriendlyErrorMessage(error, 'startup');
      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * Reset tool state when operations fail
   * @param {string} toolId - ID of the tool
   * @param {string} reason - Reason for reset
   * @returns {Promise<void>}
   */
  async _resetToolState(toolId, reason) {
    try {
      console.warn(`[ToolService] Resetting state for tool ${toolId}: ${reason}`);
      
      // Clear any stored process IDs
      try {
        localStorage.removeItem(`tool_pid_${toolId}`);
        localStorage.removeItem(`tool_running_${toolId}`);
      } catch (storageError) {
        console.warn(`[ToolService] Could not clear localStorage for ${toolId}:`, storageError.message);
      }
      
      // Attempt to cleanup partial installations
      const toolPath = storageService.getToolPath(toolId);
      const exists = await neuService.exists(toolPath);
      
      if (exists) {
        try {
          const files = await neuService.readDirectory(toolPath);
          
          // If directory seems incomplete (only zip file or very few files), clean it up
          if (files.length <= 2 || files.some(file => file.endsWith('.zip'))) {
            console.info(`[ToolService] Cleaning up incomplete installation for ${toolId}`);
            await utilityService.cleanupDirectory(toolPath);
          }
        } catch (cleanupError) {
          console.warn(`[ToolService] Could not cleanup directory for ${toolId}:`, cleanupError.message);
        }
      }
      
    } catch (error) {
      console.error(`[ToolService] Error during state reset for ${toolId}:`, error.message);
      // Don't throw here - we want to continue with user-friendly error message
    }
  }
  /**
   * Convert technical errors to user-friendly messages
   * @param {Error} error - Original error
   * @param {string} phase - Phase where error occurred
   * @returns {string} - User-friendly error message
   */
  _getUserFriendlyErrorMessage(error, phase) {
    const errorMessage = error?.message?.toLowerCase() || '';
    
    // Network-related errors
    if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('timeout')) {
      return 'Connection problem. Please check your internet connection and try again.';
    }
    
    // Download-related errors
    if (phase === 'download' || errorMessage.includes('download')) {
      if (errorMessage.includes('404') || errorMessage.includes('not found')) {
        return 'Tool download file not found. This tool may no longer be available.';
      }
      if (errorMessage.includes('403') || errorMessage.includes('unauthorized')) {
        return 'Access denied. You may not have permission to download this tool.';
      }
      return 'Download failed. Please check your internet connection and try again.';
    }
    
    // Extraction-related errors
    if (phase === 'extraction' || errorMessage.includes('extract') || errorMessage.includes('zip')) {
      return 'Failed to extract tool files. The download may be corrupted.';
    }

    // Registration/validation errors
    if (phase === 'registration' || errorMessage.includes('tool executable not found')) {
      return 'Tool installation incomplete. The executable file was not found after extraction.';
    }

    // Startup/execution errors
    if (phase === 'startup' || errorMessage.includes('spawn') || errorMessage.includes('executable')) {
      return 'Could not start the tool. It may not be compatible with your system.';
    }
    
    // Uninstall errors
    if (phase === 'uninstall' || errorMessage.includes('remove') || errorMessage.includes('delete')) {
      return 'Could not completely remove tool files. Some files may remain.';
    }
    
    // Permission errors
    if (errorMessage.includes('permission') || errorMessage.includes('access') || errorMessage.includes('eacces')) {
      return 'Permission denied. Try running as administrator or check file permissions.';
    }
    
    // Disk space errors
    if (errorMessage.includes('space') || errorMessage.includes('enospc')) {
      return 'Not enough disk space. Please free up some space and try again.';
    }
    
    // Registry/system errors
    if (errorMessage.includes('registry') || errorMessage.includes('system')) {
      return 'System configuration error. The tool may not be properly registered.';
    }
    
    // Generic fallback
    return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
  }
}

const toolService = new ToolService();
export default toolService;
